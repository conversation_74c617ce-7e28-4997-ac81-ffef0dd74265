
$(function(){



	$(window).resize(function(){
		bodywidth();
		heightC();
	});


	$('#menuchange').on('change', function () {
		$('body').addClass("overlay");
		if ($(this).prop('checked')) {
			$.when(
				$("#imenuBase").fadeIn()
			).done(function(){ 
				$("#imenuBase").css({paddintTop:"0px",opacity:"1"});
			});
		} else {
			$('body').removeClass("overlay");
			$("#imenuBase").css({opacity:"0",display:"none"});
			$("#imenuBase").fadeOut();
		}
	});

	$("a").on('click', function(){
		const file_name =  window.location.pathname.split('/').pop();
		const linkurl = $(this).attr("href");
		
		if(linkurl.indexOf(file_name)>= 0 ) {
			const elID = linkurl.indexOf("#") ;
			const target = linkurl.substring(elID);
			const scl = $(target).offset().top - 95;
			$("html,body").stop().animate({scrollTop : scl}, 800);
			window.history.pushState(null, null, linkurl);
		}else{
			location.href=linkurl;
		}
		return false;
	});


	$("#imenuBase a").on('click', function(){

		if(wsize <= "1100"){
			$("#menuchange").prop("checked", false);
			$('body').removeClass("overlay");
			$("#imenuBase").css({opacity:"0",display:"none"});
			$("#imenuBase").fadeOut();
		}
	});



	let sectionArr = new Array(),
	elIndex = $('nav .nvsLink');
	for(i=0; i < elIndex.length; i++){
		const elLink = elIndex.eq(i).attr('href');
		const elID = elLink.indexOf("#") ;
		sectionArr[i] = elLink.substring(elID);

	}



$(window).on('scroll', function(){

	let top = $(window).scrollTop() +180;
	if(elIndex.length != 1 ){
		for(i=0; i < sectionArr.length; i++){
			let target = sectionArr[i],
				secTop = $(target).offset().top,
				secBottom = secTop + $(target).outerHeight(true);
			if(secTop <= top && secBottom >= top){
				$("nav").removeClass('acNav');
				elIndex.removeClass('current');
				elIndex.eq(i).addClass('current').parent("nav").addClass('acNav');
			}
		}
	}


	hoverFix=false;

});




/*--------------------------------------------------------------------------------------*/
});

$(window).load(function () { 
	
	$('#loading').fadeOut(300);
	$("#mainFrame, #header").fadeIn(function(){
		
		window.setTimeout(function(){
			bodywidth();
			heightC();

	}, 1000);
	});
	if(location.hash){
		const link= location.hash;
		const scl = $(link).offset().top - 95;
		$("html,body").stop().animate({scrollTop : scl}, 800);
	}



});

function bodywidth(){
	wsize = $(window).width();
	hsize = $(window).height();


}

function heightC(){

	/*画像の高さを40の倍数に*/
	for(var i = 0 ; i < $(".CTextA img").length ; i++){
		hh=$('.CTextA img').eq(i).height();
		hm=Math.ceil(hh / 40);
		hma=hm*40 - hh;
		$('.CTextA img').eq(i).css({marginBottom:hma});

	}

}