# ActionPatches MMF Enhancement v2.0.0
- Short description: Unlock/add restricted or missing character actions including MMF. Can cause bugs!
- Author(s):         https://github.com/hawkeye-e and https://github.com/bogus-things
- More information:  https://github.com/bogus-things/RG_ActionPatches

# BepInEx_UnityIL2CPP_x64 v6.0.0.0
- Short description: Plugin framework
- Author(s):         https://github.com/BepInEx
- More information:  https://github.com/BepInEx/BepInEx

# BepInExConfigManager v1.3.0
- Short description: Can change plugin settings. Press F5 to open
- Author(s):         https://github.com/sinai-dev
- More information:  https://github.com/sinai-dev/BepInExConfigManager

# BepisPlugins v19.1
- Short description: Collection of essential plugins
- Author(s):         bbepis
- More information:  https://github.com/bbepis/BepisPlugins

# ColorPanelX v1.4.3
- Short description: Improves maker color picker
- Author(s):         https://github.com/kkykkykky
- More information:  https://github.com/kkykkykky

# Drag and Drop v1.2.7
- Short description: Drag cards and scenes from explorer into game window
- Author(s):         IllusionMods
- More information:  https://yuki-portal.com/uploader/honeyselect2/mod/システムパッチ-プラグイン/194386/

# Enable Full Screen Toggle v0.7
- Short description: Allow toggling full screen with Alt+Enter
- Author(s):         https://github.com/SpockBauru
- More information:  https://github.com/SpockBauru/SpockPlugins_BepInEx

# Enable Resize v0.7
- Short description: Enable resizing of game window
- Author(s):         https://github.com/SpockBauru
- More information:  https://github.com/SpockBauru/SpockPlugins_BepInEx

# Full Save
- Short description: Unlock FreeH, all maps and all H positions. Doesn't overwrite game saves
- Author(s):         bytemask
- More information:  IS Discord server

# Graphics Settings v0.7
- Short description: More graphics settings, press F5 to access
- Author(s):         https://github.com/SpockBauru
- More information:  https://github.com/SpockBauru/SpockPlugins_BepInEx

# Gravure plugin v1.0
- Short description: More posing options in character maker, press G
- Author(s):         Mikke
- More information:  IS Discord server

# HairMeshforAll v1.1.0
- Short description: Unlock mesh customization options for all hair
- Author(s):         https://github.com/kkykkykky
- More information:  https://github.com/kkykkykky/HairMeshforAll

# HSceneCrowdReaction v1.0.0
- Short description: Give characters different reactions when seeing a H scene instead of only being awkward
- Author(s):         https://github.com/hawkeye-e
- More information:  https://github.com/hawkeye-e/RG_HSceneCrowdReaction

# IllusionFixes_IL2CPP v21.2.1
- Short description: A collection of essential fixes and improvements
- Author(s):         https://github.com/IllusionMods
- More information:  https://github.com/IllusionMods/IllusionFixes

# IllusionLaunchers_RoomGirl v3.4.1.36434
- Short description: Custom game launcher
- Author(s):         https://github.com/IllusionMods
- More information:  https://github.com/IllusionMods/IllusionLaunchers

# KKManager v0.23.0.0
- Short description: Manage and update mods, browse cards
- Author(s):         https://github.com/IllusionMods
- More information:  https://github.com/IllusionMods/KKManager

# Lock H-Menu v0.2
- Short description: Adds an icon to make the left menu in H scenes stop hiding automatically
- Author(s):         https://github.com/SpockBauru
- More information:  https://github.com/SpockBauru/SpockPlugins_Illusion

# Maker Screenshot v1.0.0
- Short description: Allows taking screenshots in character creator (press F11)
- Author(s):         https://github.com/kkykkykky
- More information:  https://github.com/kkykkykky/MakerScreenShot

# MaterialMod v0.2.0
- Short description: An (Material Editor) made from zero for Room Girl
- Author(s):         SpockBauru
- More information:  https://github.com/SpockBauru/SpockPlugins_Illusion/releases

# Message Center v0.7
- Short description: Allows plugins to show messages in top left corner of the game
- Author(s):         https://github.com/SpockBauru
- More information:  https://github.com/SpockBauru/SpockPlugins_BepInEx

# MovementControl v0.1.0
- Short description: A simple tool to force the characters to stay on their existing positions
- Author(s):         HP
- More information:  https://github.com/hawkeye-e/RG_MovementControl

# Mute In Background v0.7
- Short description: Mute the game when it's not in focus, turn on in plugin settings
- Author(s):         https://github.com/SpockBauru
- More information:  https://github.com/SpockBauru/SpockPlugins_BepInEx

# PovX v0.0.3
- Short description: Adds first-person view in H scenes, check hotkeys in plugin settings (F5)
- Author(s):         https://github.com/bogus-things
- More information:  https://github.com/bogus-things/RG_PovX

# RG Cheats v1.1
- Short description: Simple trainer for Room Girl, appears next to character portrait
- Author(s):         https://github.com/SpockBauru/SpockPlugins_Illusion
- More information:  https://github.com/SpockBauru/SpockPlugins_Illusion/releases

# Roomgirl_Uncensor v1.1
- Short description: A simple female, male and futa uncensor. Color can be changed with the 'UNC1' Paint decal in character maker.
- Author(s):         cur144
- More information:  IS Discord server

# Studio pose pack
- Short description: A large collection of poses, use in Studio through the anim/Kinematics/Pose menu
- Author(s):         awpzzggg
- More information:  IS Discord server

# StudioCharaSort v1.1.0
- Short description: Change default sorting of character and coordinate cards in studio
- Author(s):         https://github.com/kkykkykky
- More information:  https://github.com/kkykkykky/StudioCharaSort

# StudioScreenshotX v1.0.0
- Short description: Various improvements to the screenshot function (press F11)
- Author(s):         https://github.com/kkykkykky
- More information:  https://github.com/kkykkykky/ScreenshotX

# Subtitles v0.1
- Short description: Show subtitles for girl's voice in H-Scenes
- Author(s):         SpockBauru
- More information:  https://github.com/SpockBauru/SpockPlugins_Illusion/releases

# UnityExplorer v4.9.0
- Short description: Developer tool and universal trainer, press F7 to open
- Author(s):         https://github.com/sinai-dev
- More information:  https://github.com/sinai-dev/UnityExplorer

# UniverseLib.Il2Cpp.Unhollower v1.5.1
- Short description: Library for making plugins for IL2CPP and Mono Unity games, with focus on UI
- Author(s):         https://github.com/sinai-dev
- More information:  https://github.com/sinai-dev/UniverseLib

# UnrestrictedHScene v0.1.1
- Short description: Enables positions added in PD expansion on all maps
- Author(s):         HP
- More information:  https://github.com/hawkeye-e/RG_UnrestrictedHScene

# Workaholics v1.0.1
- Short description: Force male characters to load in at their workplaces after time changes
- Author(s):         https://github.com/bogus-things
- More information:  https://github.com/bogus-things/RG_Workaholics

# XUnity Auto Translator v5.2.0
- Short description: Translation loader
- Author(s):         https://github.com/bbepis
- More information:  https://github.com/bbepis/XUnity.AutoTranslator

# XUnity Resource Redirector v2.1.0
- Short description: Modding API for overriding game resources
- Author(s):         https://github.com/bbepis
- More information:  https://github.com/bbepis/XUnity.AutoTranslator

