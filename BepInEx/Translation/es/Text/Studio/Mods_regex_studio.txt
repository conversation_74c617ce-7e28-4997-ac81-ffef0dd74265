//Regexes for mods and items translations

#set exe RoomStudio

// Translate a non latin word followed by latin letters or numbers, so the first word needs to be translated just once.
// Example: Instead of "シャツA=Shirt A", "シャツ2=Shirt 2" and "シャツ[a4]=Shirt [a4]", only "シャツ=Shirt" is needed.
// OBS: It needs to have at least one non latin letter in the beginning followed by one latin letter or number, otherwise it would break things in other places.
sr:"^(?<nonLatin>[^\u0000-\u024F]+)(?<latin_i>[\u0020-\u024F｢【｣】]+)$"=${nonLatin} ${latin_i}

// Same as above but the non latin word is in the middle. 
// Example: Instead of "[Modder]シャツ(F_5)=[Modder] Shirt (F_5)", only "シャツ=Shirt" is needed
sr:"^(?<latin_i>[\u0020-\u024F｢【｣】]+)(?<nonLatin>[^\u0000-\u024F]+)(?<latin2_i>[\u0020-\u024F｢【｣】]*)$"=${latin_i} ${nonLatin} ${latin2_i}
