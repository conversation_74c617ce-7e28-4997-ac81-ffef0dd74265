// some of these conflict with maker which needs shorter names
// ensure this file is only loaded for Studio

#set exe RoomStudio


// Most everything in studio is translated by files found under
// RedirectedResources\assets\abdata\studio\info
// This file should mainly contain UI elements not included in assets

//
// Right Sidebar
//

ワークスペース=Workspace

// Right Sidebar -> Camera
r:"^カメラ([０-９0-9]+)$"=Camera $1

// Camera Animation Settings
名前=Name
動作=Active
フル=Loop

// Right Sidebar -> Route
ルート=Route
子接続先=Child Access Point
スタート=Start
r:"^ポイント([０-９0-9]+)$"=Point $1

// Route Animation Settings
ルート設定=Route Settings
ルートコントローラー=Route Controller
//Name
追加=Add Point
向き補正=Correct Aim
//Loop
ライン描画=Motion Line
//Line Color
ルートのラインカラー=Route Line Color

// Route Point Settings
ルートポイント設定=Route Point Settings
//Speed
挙動=Behavior
形状=Shape
直線=Line
曲線=Curve
連結=Link

// Route Point Behaviors
直線的=Linear
徐々に早く=Speeds Up
徐々に遅く=Slows Down
急に早く=Immediate Fast
急に遅く=Immediate Slow
バウンド=Bound

// Right Sidebar -> Added Character

sr:"^グループ [:：] (.*)$"=Group: $1
sr:"^部位 [:：] (.*)$"=Part: $1

髪=Hair
頭=Head
顔=Face
首=Neck
胴=Torso
腰=Waist
脚=Legs
腕=Arms
手=Hands
股間=Crotch
ポニー=Ponytail
ツイン左=Left Twintail
ツイン右=Right Twintail
ヘアピン左=Left Hairpin
ヘアピン右=Right Hairpin
帽子=Hat
額=Forehead
頭上=Top of Head
頭中心=Center of Head
イヤリング左=Left Earring
イヤリング右=Right Earring
眼鏡=Glasses
鼻=Nose
口=Mouth
首=Neck
胸上=Upper Chest
胸上中央=Center Of Upper Chest
左乳首=Left Nipple
右乳首=Right Nipple
背中中央=Center Of Back
背中左=Left Of Back
背中右=Right Of Back
腰=Waist
腰前=Front Of Waist
腰後ろ=Rear Of Waist
腰左=Left Waist
腰右=Right Waist
左太もも=Left Thigh
左ひざ=Left Knee
左足首=Left Ankle
かかと左=Left Heel
右太もも=Right Thigh
右ひざ=Right Knee
右足首=Right Ankle
かかと右=Right Heel
左肩=Left Shoulder
左上腕=Left Upper Arm
左ひじ=Left Elbow
左手首=Left Wrist
右肩=Right Shoulder
右上腕=Right Upper Arm
右ひじ=Right Elbow
右手首=Right Wrist
左手=Left Hand
左人差指=Left Index Finger
左中指=Left Middle Finger
左薬指=Left Ring Finger
右手=Right Hand
右人差指=Right Index Finger
右中指=Right Middle Finger
右薬指=Right Ring Finger
男根根本=Base Of Penis
女性器=Female Genitalia
尻穴=Anus
顔=Face
左耳=Left Ear
右耳=Right Ear
左胸=Left Chest
右胸=Right Chest
左肘=Left Elbow
右肘=Right Elbow
下腹部①=Lower Abdomen ①
下腹部②=Lower Abdomen ②
下腹部③=Lower Abdomen ③

// Right Sidebar -> Default New Folder
フォルダー=New Folder
名前を入力=Enter A Name

// New Camera control from Patch 0727
設定しているカメラ=Set Camera

//
// Color Picker
//

左クリックで現在のカラーを登録します=Left-click to copy current color
全削除=Delete All

// Picker
ピッカー=Picker
スライダー=Slider
プリセット=Presets
サンプル=Sample
見本=Result
色合い=Hue
鮮やかさ=Saturation
明るさ=Value
赤=Red
緑=Green
青=Blue
透明度=Alpha

//
// Object Controller
//

オブジェクトコントローラー=Object Controller
平行移動=Horiz.
上下移動=Vert.
r:"^([Ａ-Ｚ])回転$"=$1 Rot.

//
// Map Controller
//
マップコントローラー=Map Controller
位置=Location
//Horiz.
//Vert.
時間帯=Time Of Day

//
// Item Settings
//

//Color 1
柄=Pattern
柄の色=Pattern Color
繰り返す=Tile Pattern
横移動=Horizontal
縦移動=Vertical
横スケール=Hor. Scale
縦スケール=Vert. Scale
//Rotator
//Shadowing
アルファ=Alpha
//Line Color
//Line Thickness
//FK
揺れ制御=Shake Control

// Item Settings -> Lower Right Box (Color picker)
r:"^アイテム カラー([０-９0-9]+)$"=Item Color $1
r:"^柄の色([０-９0-9]+)$"=Pattern Color $1

// Item Settings -> Patterns
柄を選択して下さい=Please Select A Pattern

// Regexes in case further variations added by patch or mods
r:"^チェック([A-Z])$"=Checkered $1
r:"^星([A-Z])$"=Stars $1

// TODO: Check if studio loads pattern list names from:
// TODO:   RedirectedResources\assets\abdata\list\characustom\*\st_pattern_*
// TODO: for not replicate here
ストライプ=Vertical Stripes
ボーダー=Horizontal Stripes
チェックA=Checkered A
水玉=Polka Dots
イチゴ=Strawberries
ハーリキンチェック=Harlequin
星B=Stars B
ハート=Heart
チェックB=Checkered B
千鳥格子=Hound's Tooth
網=Net
ヒョウ=Leopard
ゼブラ=Zebra
迷彩=Camouflage
レンガ=Brick
編み込み=Weaved
市松=Checkered
麻の葉=Hexagons
青海波=Ocean Waves
唐草=Arabesque
矢絣=Arrow Feathers
ノルデック=Nordic
鹿子=Deerskin
亀甲花菱=Turtleshell Flowers
星A=Stars A
ドクロ=Skull
グラデーションボーダー=Gradient
花柄=Flowers
縦線=Vertical Lines
横線=Horizontal Lines
モザイク=Mosaic
アーガイル=Argyle
チマヨ=Chimayo
ギンガムチェック=Gingham Checkered
変則ストライプ=Irregular Vertical Stripes
変則ボーダー=Irregular Horizontal Stripes
丸=Circle
ペイズリー=Paisley
エイシェント=Ancient
南国リーフ=Tropical Leaves
ハイビスカス=Hibiscus
スポーツ=Sports
ストライプ細=Thin Stripes
バラ=Rose
汚れ=Dirty
牛柄=Cow
蛇柄=Snake
牡丹=Peony
雪輪=Round Snow
毬=Bouquet
菊=Chrysanthemum
桜=Cherry Blossoms
霞桜=Falling Cherry Blossoms
//八つ藤=
鳥流水=Bird in Water Flow
大花柄=Large Floral
菊唐草=Chrysanthemum arabesque
梅唐草=Plum Arabesque
松皮菱=Pine Skin Diamond

//
// Map Light Stuff
//

//Color
ライトのON/OFF=Light On/Off
ターゲット表示=Show Target
//Self-Shadowing
//Strength
//Intensity
スポット角度=Spot Angle
ライト=Light // Lower Right Box

//
// Add Menu
//

女キャラ=Girls
男キャラ=Boys
アイテム=Items
マップ=Map
マップライト=Map Lights
背景=Background
フレーム=Frame

// Groups/Categories/Items are loaded from assets matching patterns below
// Regexes in this section are kept in case further variations added by patch or mods

// Add -> Group
// RedirectedResources\assets\abdata\studio\info\*\itemgroup_*

// Add -> Group -> Category
// RedirectedResources\assets\abdata\studio\info\*\itemcategory_*

r:"^追加([０-９0-9]+)$"=Additional $1

// Add -> Group -> Category -> Items
// RedirectedResources\assets\abdata\studio\info\*\itemlist_*





// Add -> Items -> Base -> Ground
r:"^畳([０-９0-9]+)$"=Tatami Mat $1
r:"^アスファルト床([０-９0-9]+)$"=Asphalt Floor $1
r:"^石床([０-９0-9]+)$"=Stone Floor $1
r:"^石畳([０-９0-9]+)$"=Stone Paving $1
r:"^レンガ床([０-９0-9]+)$"=Brick Floor $1
r:"^タイル床([０-９0-9]+)$"=Tile Floor $1 // Because the first one is goddamn fullwidth
r:"^砂地([０-９0-9]+)$"=Sand $1
r:"^花壇([０-９0-9]+)$"=Flower Bed $1


// Add -> Items -> Base -> Doors
r:"^襖([０-９0-9]+)$"=Sliding Door $1
r:"^障子([０-９0-9]+)$"=Paper Door $1
r:"^トイレドア([０-９0-9]+)$"=Toilet Door $1

// Add -> Items -> Base -> Walls
r:"^石壁([０-９0-9]+)$"=Stone Wall $1
r:"^ブロック壁([０-９0-9]+)$"=Block Wall $1

// Add -> Items -> Base -> Stairs
r:"^石階段([０-９0-9]+)$"=Stone Stairs $1

// Add -> Items -> Base -> Installed Objects
r:"^壁ボックス([０-９0-9]+)$"=Wall Box $1
r:"^ホワイトボード([０-９0-9]+)$"=Whiteboard $1
r:"^岩([０-９0-9]+)$"=Rock $1

// Add -> Items -> Base -> Structures
r:"^ビル([０-９0-9]+)$"=Building $1

// Add -> Items -> Furniture -> Desks
r:"^机([０-９0-9]+)$"=Desk $1
r:"^ちゃぶ台([０-９0-9]+)$"=Chabudai $1

// Add -> Items -> Furniture -> Chairs
r:"^椅子([０-９0-9]+)$"=Chair $1
r:"^食堂椅子([０-９0-9]+)$"=Dining Chair $1
r:"^パイプ椅子([０-９0-9]+)$"=Pipe Chair $1
r:"^長椅子([０-９0-9]+)$"=Gym Bench $1
r:"^ソファ([０-９0-9]+)$"=Sofa $1
r:"^豪華ソファ([０-９0-9]+)$"=Expensive Sofa $1
r:"^ベンチ([０-９0-9]+)$"=Bench $1
r:"^和風長椅子([０-９0-9]+)$"=Japanese Chair $1

// Add -> Items -> Furniture -> Shelves
r:"^ロッカー([０-９0-9]+)$"=Locker $1
r:"^壁棚([０-９0-9]+)$"=Wall Shelf $1
r:"^小棚([０-９0-9]+)$"=Small Shelf $1
r:"^事務棚([０-９0-9]+)$"=Office Shelf $1
r:"^大棚([０-９0-9]+)$"=Large Shelf $1
r:"^自室棚([０-９0-9]+)$"=Bedroom Shelf $1
r:"^図書室棚([０-９0-9]+)$"=Library Shelf $1
r:"^棚([０-９0-9]+)$"=Shelf $1

// Add -> Items -> Furniture -> Dividers
r:"^カーテンレール([０-９0-9]+)$"=Curtain Rails $1
r:"^カーテン([０-９0-9]+)$"=Curtain $1
r:"^ブラインド([０-９0-9]+)$"=Blinds $1
r:"^シャワー室仕切り([０-９0-9]+)$"=Shower Divider $1
r:"^トイレ仕切り([０-９0-9]+)$"=Toilet Divider $1

// Add -> Items -> Furniture -> Bathroom
r:"^便器([０-９0-9]+)$"=Toilet $1
r:"^トイレットペーパー([０-９0-9]+)$"=Toiletpaper $1
r:"^蛇口([０-９0-9]+)$"=Faucet $1
r:"^洗面器([０-９0-9]+)$"=Sink $1

// Add -> Items -> Furniture -> Summer
ビーチチェア=Beach Chair
ビーチパラソル=Beach Parasol
木の椅子=Wooden Chair
木の長椅子=Wooden Bench
木の机=Wooden Desk
木の箱=Wooden Box

//
// Add -> Items -> Ornaments
//

r:"^プレゼント([０-９0-9]+)$"=Present $1
r:"鉄パイプ([０-９0-9]+)$"=Iron Pipe $1
r:"流木([０-９0-9]+)$"=Driftwood $1
r:"板([０-９0-9]+)$"=Board $1
r:"レンガ([０-９0-9]+)$"=Brick $1


// Add -> Items -> Objects -> Sports
r:"^マット([０-９0-9]+)$"=Mat $1
r:"^跳び箱([０-９0-9]+)$"=Vaulting Horse $1
r:"^ボール([０-９0-9]+)$"=Ball $1
r:"^プールロープ([０-９0-9]+)$"=Pool Rope $1
r:"^飛び込み台([０-９0-9]+)$"=Diving Board $1
r:"^手すり([０-９0-9]+)$"=Handrail $1

// Add -> Items -> Objects -> Plants
r:"^観葉植物([０-９0-9]+)$"=Decorative Plant $1
r:"^生垣([０-９0-9]+)$"=Hedge $1

// Add -> Items -> Objects -> Afterschool
r:"^ステンドグラス([０-９0-9]+)$"=Stained Glass $1
r:"^教会の柱([０-９0-9]+)$"=Church Pillar $1
r:"^燭台([０-９0-9]+)$"=Candlestick $1
r:"^音楽機器([０-９0-9]+)$"=Karaoke Machine $1
r:"^充電器([０-９0-9]+)$"=Charging Dock $1
r:"^音響機器([０-９0-9]+)$"=Loudspeaker $1
r:"^照明機器([０-９0-9]+)$"=Stage Lighting $1

//
// Add -> Items -> Objects -> Foodstuffs
//

// Add -> Items -> Objects -> Foodstuffs -> Food
r:"^弁当([０-９0-9]+)$"=Bento $1
r:"^サンドイッチ([０-９0-9]+)$"=Sandwich $1
r:"^メロンパン([０-９0-9]+)$"=Melon Bread $1
r:"^定食([０-９0-9]+)$"=Full Meal $1
r:"^バナナ([０-９0-9]+)$"=Banana $1

//
// Add -> Items -> Objects -> Small Articles
//

// Add -> Items -> Objects -> Small Articles -> Daily Life
r:"^ノート([０-９0-9]+)$"=Notebook $1
r:"^バケツ([０-９0-9]+)$"=Bucket $1
r:"^踏み台([０-９0-9]+)$"=Stool $1
r:"^カレンダー([０-９0-9]+)$"=Calendar $1
r:"^本([０-９0-9]+)$"=Book $1
r:"^見開き本([０-９0-9]+)$"=Opened Book $1
r:"^ゴミ箱([０-９0-9]+)$"=Garbage Can $1
r:"^ダンボール([０-９0-9]+)$"=Cardboard $1
r:"^紙箱([０-９0-9]+)$"=Paper Box $1

// Add -> Items -> Objects -> Small Articles -> General Goods
r:"^壁掛け([０-９0-9]+)$"=Wall Decoration $1
r:"^掲載物([０-９0-9]+)$"=Notice $1 // Because the fourth one is goddamn fullwidth
r:"^プラモデル([０-９0-9]+)$"=Plastic Model $1
r:"^大型定規([０-９0-9]+)$"=Large Ruler $1
r:"^壺([０-９0-9]+)$"=Vase $1
r:"^座布団([０-９0-9]+)$"=Zabuton $1
r:"^掛け軸([０-９0-9]+)$"=Hanging Scroll $1
r:"^陶器([０-９0-9]+)$"=Earthenware $1

// Add -> Items -> Objects -> Small Articles -> Appliances
r:"^時計([０-９0-9]+)$"=Clock $1
r:"^テレビ([０-９0-9]+)$"=TV $1
r:"^冷水器([０-９0-9]+)$"=Water Fountain $1
r:"^自販機([０-９0-9]+)$"=Vending Machine $1
r:"^たこ足配線([０-９0-9]+)$"=Power Strip $1
r:"^コンセントコード([０-９0-9]+)$"=Electrical Cord $1

// Add -> Items -> Objects -> Small Articles -> Fantasy
r:"^宝箱([０-９0-9]+)$"=Treasure Chest $1

//
// Add -> Items -> Character
//

// Add -> Items -> Character -> Hair
r:"^細リボン([A-Z])$"=Fine Ribbon $1

// Add -> Items -> Character -> Head
r:"^あほ毛([A-Z])$"=Ahoge $1
r:"^くるくるあほ毛([A-Z])$"=Curly Ahoge $1
r:"^ギザギザあほ毛([A-Z])$"=Jagged Ahoge $1
r:"^ちょびあほ毛([A-Z])$"=Tiny Ahoge $1
r:"^ハートあほ毛([A-Z])$"=Heart Ahoge $1

// Add -> Items -> Character -> Face
r:"^ハート([A-Z])$"=Heart $1


// Add -> Items -> Character -> Afterschool
r:"^付け毛([０-９0-9][０-９0-9]+)$"=Hairpiece $1


//
// Add -> Items -> 2D Effects
//

// Add -> Items -> 2D Effects -> Speech Balloons

// Thanks for doing #1 as a fullwidth numeral and everything else as halfwidth, Illusion
// At least you didn't mix up the parentheses unlike in the general/chara model items
// Illusion buggered the first kanji in some of these - should be 楕, but it's 惰)
// RegExes will catch both variations

r:"^丸吹き出し([０-９0-9]+)左$"=Oval $1 Left
r:"^丸吹き出し([０-９0-9]+)左\\(影あり\\)$"=Oval $1 Left (Shadowed)
r:"^丸吹き出し([０-９0-9]+)右$"=Oval $1 Right
r:"^丸吹き出し([０-９0-9]+)右\\(影あり\\)$"=Oval $1 Right (Shadowed)
r:"^角吹き出し([０-９0-9]+)左$"=Angular $1 Left
r:"^角吹き出し([０-９0-9]+)左\\(影あり\\)$"=Angular $1 Left (Shadowed)
r:"^角吹き出し([０-９0-9]+)右$"=Angular $1 Right
r:"^角吹き出し([０-９0-9]+)右\\(影あり\\)$"=Angular $1 Right (Shadowed)
r:"^四角吹き出し([０-９0-9]+)左$"=Rectangular $1 Left
r:"^四角吹き出し([０-９0-9]+)左\\(影あり\\)$"=Rectangular $1 Left (Shadowed)
r:"^四角吹き出し([０-９0-9]+)右$"=Rectangular $1 Right
r:"^四角吹き出し([０-９0-9]+)右\\(影あり\\)$"=Rectangular $1 Right (Shadowed)
r:"^正円吹き出し([０-９0-9]+)左$"=Circular $1 Left
r:"^正円吹き出し([０-９0-9]+)左\\(影あり\\)$"=Circular $1 Left (Shadowed)
r:"^正円吹き出し([０-９0-9]+)右$"=Circular $1 Right
r:"^正円吹き出し([０-９0-9]+)右\\(影あり\\)$"=Circular $1 Right (Shadowed)
r:"^雲吹き出し([０-９0-9]+)左$"=Cloud $1 Left
r:"^雲吹き出し([０-９0-9]+)左\\(影あり\\)$"=Cloud $1 Left (Shadowed)
r:"^雲吹き出し([０-９0-9]+)右$"=Cloud $1 Right
r:"^雲吹き出し([０-９0-9]+)右\\(影あり\\)$"=Cloud $1 Right (Shadowed)
r:"^雷吹き出し([０-９0-9]+)左$"=Lightning $1 Left
r:"^雷吹き出し([０-９0-9]+)左\\(影あり\\)$"=Lightning $1 Left (Shadowed)
r:"^雷吹き出し([０-９0-9]+)右$"=Lightning $1 Right
r:"^雷吹き出し([０-９0-9]+)右\\(影あり\\)$"=Lightning $1 Right (Shadowed)
r:"^回想吹き出し([０-９0-9]+)左$"=Thinking $1 Left
r:"^回想吹き出し([０-９0-9]+)左\\(影あり\\)$"=Thinking １ Left (Shadowed)
r:"^回想吹き出し([０-９0-9]+)右$"=Thinking $1 Right
r:"^回想吹き出し([０-９0-9]+)右\\(影あり\\)$"=Thinking １ Right (Shadowed)
r:"^[楕惰]円吹き出し([０-９0-9]+)左$"=Ellipse $1 Left
r:"^[楕惰]円吹き出し([０-９0-9]+)左\\(影あり\\)$"=Ellipse $1 Left (Shadowed)
r:"^[楕惰]円吹き出し([０-９0-9]+)右$"=Ellipse $1 Right
r:"^[楕惰]円吹き出し([０-９0-9]+)右\\(影あり\\)$"=Ellipse $1 Right (Shadowed)
r:"^棘吹き出し([０-９0-9]+)左$"=Shocked $1 Left
r:"^棘吹き出し([０-９0-9]+)左\\(影あり\\)$"=Shocked $1 Left (Shadowed)
r:"^棘吹き出し([０-９0-9]+)右$"=Shocked $1 Right
r:"^棘吹き出し([０-９0-9]+)右\\(影あり\\)$"=Shocked $1 Right (Shadowed)
r:"^先なし吹き出し\\(楕円([０-９0-9]+)\\)$"=No Tip (Ellipse $1)
r:"^先なし吹き出し\\(四角([０-９0-9]+)\\)$"=No Tip (Rectangular $1)
r:"^吹き出し先([０-９0-9]+)$"=Tip $1
r:"^色影\\(四角([０-９0-9]+)\\)$"=Colored Shadow (Square $1)

// Add -> Items -> 2D Effects -> Scene Effects
r:"^レンズフレア([A-Z])$"=Lens Flare $1

//
// Add -> Items -> Gimmicks
//

// Add -> Items -> Gimmicks -> With Objects
r:"^関節([０-９0-9]+)$"=$1° Joint
r:"^投げ([０-９0-9]+)$"=Pitch $1
r:"^関節([０-９0-9]+)$"=$1° Joint
r:"^関節([０-９0-9]+)_Bタイプ$"=$1° Joint Type B

// Add -> Items -> Gimmicks -> Without Objects
r:"^回転([０-９0-9]+)（obj無し）$"=$1° Rotator (Without Object)
r:"^バウンド([０-９0-9]+)（obj無し）$"=Bound $1 (Without Object)
r:"^投げ([０-９0-9]+)（obj無し）$"=Pitch $1 (Without Object)
r:"^回転([０-９0-9]+)_Bタイプ（obj無し）$"=$1° Rotator Type B (Without Object)

//
// Add -> Items -> 3D SFX
//

// Add -> Items -> 3D SFX -> Voices
r:"^喘ぎ([０-９0-9]+)$"=Panting $1
r:"^フェラ([０-９0-9]+)$"=Blowjob $1


// AIS
// TODO: verify we don't need these and delete them
ポジティブ(喘ぎ2)=Positive (Panting 2)
ポジティブ(喘ぎ1)=Positive (Panting 1)
ポジティブ(フェラ2)=Positive (Blowjob 2)
ポジティブ(フェラ1)=Positive (Blowjob 1)
ものぐさ(喘ぎ2)=Lazy (Panting 2)
ものぐさ(喘ぎ1)=Lazy (Panting 1)
ものぐさ(フェラ2)=Lazy (Blowjob 2)
ものぐさ(フェラ1)=Lazy (Blowjob 1)
わがまま(喘ぎ2)=Selfish (Panting 2)
わがまま(喘ぎ1)=Selfish (Panting 1)
わがまま(フェラ2)=Selfish (Blowjob 2)
わがまま(フェラ1)=Selfish (Blowjob 1)
自信家(喘ぎ2)=Confident (Panting 2)
自信家(喘ぎ1)=Confident (Panting 1)
自信家(フェラ2)=Confident (Blowjob 2)
自信家(フェラ1)=Confident (Blowjob 1)
おおらか(喘ぎ2)=Friendly (Panting 2)
おおらか(喘ぎ1)=Friendly (Panting 1)
おおらか(フェラ2)=Friendly (Blowjob 2)
おおらか(フェラ1)=Friendly (Blowjob 1)
無感情(喘ぎ2)=Emotionless (Panting 2)
無感情(喘ぎ1)=Emotionless (Panting 1)
無感情(フェラ2)=Emotionless (Blowjob 2)
無感情(フェラ1)=Emotionless (Blowjob 1)

//
// Add -> Items -> Weapons
//

// Add -> Items -> Effects -> Fixed Effects
r:"^溶解粘液([０-９0-9]+)$"=Dissolving Liquids $1

//
// Add -> Items -> Glow
//

// Add -> Items -> Glow -> Illumination
r:"^電気照明([０-９0-9]+)$"=Electric Light $1
r:"^電気照明\\(和\\)([０-９0-9]+)$"=Soft Electric Light $1
r:"^電気スタンド([０-９0-9]+)$"=Floor Lamp $1
r:"^非常灯([０-９0-9]+)$"=Emergency Light $1


// Add -> Items -> Glow -> Mecha
r:"^シンプルコア([０-９0-9]+)$"=Simple Core $1

// Add -> Items -> FK Items -> General
r:"^FK先端([０-９0-9]+)$"=FK Tip $1
r:"^土台([０-９0-9]+)$"=Base $1

// Add -> Items -> FK Items -> H-Items
r:"^FK触手([０-９0-9]+)$"=FK Tentacle $1
r:"^FKアナルビーズ([０-９0-9]+)$"=FK Anal Beads $1

//
// Add -> Map Lights
//

全体光=Entire Map
ポイントライト=Point Light
スポットライト=Spotlight
スポットライト(マップ)=Spotlight (Map)
ポイントライト(マップ)=Point Light (Map)
全体光(マップ)=Whole Light (Map)
スポットライト(キャラ)=Spotlight (Char)
ポイントライト(キャラ)=Point Light (Char)
全体光(キャラ)=Whole Light (Char)

//
// Animation Menu
//

状態=Current State
キネマ=Kinematics
アニメ=Animations
ボイス=Voice
衣装=Clothing
関節補正=Joint Adjustment

//
// Animation Menu -> Current State
//

服装=Clothing Menu
タイプ=Type
一括=Set All
アイテム設定=Item settings
服装詳細=Clothes
 服装 一括= All Clothes

// Animation Menu -> Current State -> Clothing Settings
トップ=Top
ボトム=Bottom
ブラ=Bra
ショーツ=Underwear
パンスト=Pantyhose
手袋=Gloves
靴下=Legwear
靴 =Shoes

// Animation Menu -> Current State -> Accessories
r:"^スロ([０-９0-9][０-９0-9]+)$"=Slot $1

// or if you have Studio Accesory Names plugin you need these
r:"^スロット([０-９0-9][０-９0-9]+)$"=Slot $1
sr:"^(?<slot_num_i>[０-９0-9]{2})\s+(?<accessory>\S.*)$"=${slot_num_i} ${accessory}


// Animation Menu -> Current State -> Fluids
汁=Fluids
顔=Face
胸=Chest
背中=Back
腹=Stomach
お尻=Butt

// Animation Menu -> Current State -> Miscellaneous
涙Lv=Tears Level
頬赤=Face Flushing
乳首=Nipple Stiffness
男根=Penis
男根サイズ=Penis Size
 涙= Tears
 濡れ= Wetness
 肌艶= Skin Gloss

// Animation Menu -> Current State -> Males Only
単色化=Monocolor
カラー=Color

//
// Animation Menu -> Kinematics
//

FK=FK
IK=IK
視線=Gaze
首操作=Neck
眉・目・口=Face
手=Hands
ポーズ=Pose

// Animation Menu -> Kinematics -> Kinematics General
機能=Active
表示=Display
サイズ=Size

// Animation Menu -> Kinematics -> Forward Kinematics
髪=Hair
首=Neck
胸=Chest
体=Body
右手=Right Hand
左手=Left Hand
スカート=Skirt

// Animation Menu -> Kinematics -> Inverse Kinematics
一括=Set All
体=Body
右手=Right Hand
左手=Left Hand
右足=Right Foot
左足=Left Foot

// Animation Menu -> Kinematics -> Eyebrows / Eyes / Mouth
種類=Pattern
開き=Openness
瞬き=Blinking
口パク=Lip-Sync

// Animation Menu -> Kinematics -> Eyebrows / Eyes / Mouth -> Display Types
コンフィグ依存=From Config
//General
最前面=Frontmost

// Animation Menu -> Kinematics -> Pose
NoName=No Name

//
// Animation Menu -> Character Animation
//

キャラ=Character
スタジオ=Studio
H愛撫=H-Caressing
女H奉仕=Girl Service
男H奉仕=Male Service
女H挿入=Girl Insertion
男H挿入=Male Insertion
H特殊=Special H
// Character Design Addon Pack Vol. 3 (add09)
バトル=Battle
// Patch 0525
レズ=Lesbian
// Patch 0727
アウトドア=Outdoors
//Summer
// Afterschool DLC
H女複数=Multi-Girl H
//Afterschool

// Animation Controller

速度=Speed
パターン=Pattern
r:"^補助([０-９0-9]+)$"=Extra $1
付属アイテム表示=Show Attached Items
強制ループ=Force Looping
アニメーション操作=Animation Management

//
// Animation Menu -> Character Animation -> Voice
//

// RedirectedResources\assets\abdata\studio\info\*\voicegroup_*
// RedirectedResources\assets\abdata\studio\info\*\voicecategory_*

//
// Animation Menu -> Character Animation -> Joint Adjustment
//

右腕=Right Forearm
左腕=Left Forearm
//Right Foot
//Left Foot
左前腕部=Left Forearm Group
右前腕部=Right Forearm Group
右太もも=Right Thigh
左太もも=Left Thigh

//
// Animation Menu -> Item Animation
//

ライトの影響度=Light Influence
r:"^カラー([０-９0-9]+)$"=Color $1
影=Shadowing
ラインの色=Line Color
ラインの太さ=Line Thickness

//
// Sound Menu
//

BGM=Game BGM
外部読込=Load External File

// Sound Menu -> Game BGM

// TODO: see if these are covered by 
// RedirectedResources\assets\abdata\studio\info\*\bgm_*
独り言=Monologue
シャンの家=Shan's House
遠い昔の街=Distant City
今の世界=Present World
暗い森=Dark Forest
森と滝=Forest And Waterfall
星空と浜辺=Starry Sky And Beach
昼の砂浜=Sandy Beach At Noon
古の研究=Ancient Research
廃墟と少女=Ruins And Girl
メインタイトル=Title Screen

//
// System Menu
//

コンフィグ=Config
オプション=Options
ロゴ=Logo
キャラライト=Character Lighting
//Scene Effects
セーブ=Save
ロード=Load
初期化=Reset
終了=Quit

// System Menu -> Config
背景色=Background Color
モブの色=Mob Char. Color
モブ色=Mob Color // For the picker
マップ遮蔽処理=Do Map Shading
// What the hell do these next two options do? They don't seem to change jack on characters.
眉の最前面表示=Brows Through Hair
目の最前面表示=Eyes Through Hair
オーディオ設定=Audio Settings
マスター音量=Master Volume
ＢＧＭ=BGM
効果音=SFX
システム音=System
環境音=Environment
音声=Voices

// System Menu -> Options
カメラ設定=Camera Settings
r:"^カメラ速度([A-Z])軸$"=Camera $1 Speed
カメラ移動速度=Camera Movement Speed
操作軸の設定=Axis Settings
大きさ=Size
速度(移動量)=Speed (Movement Rate)
スナップ移動幅=Grid Snap Amount
オブジェクトの設定=Object Settings
追加位置=Spawn Location
原点=Origin
注視点=Crosshair
表示タイプ=Display Type
全表示=Show All
選択表示=Selected
追加時の自動表示=Automatically Show
する=Yes
しない=No
追加時の自動選択=Automatically Select
キャラFK設定=Character FK Settings
カラー設定=Color Settings
FKカラー 髪=FK Color: Hair
FKカラー 首=FK Color: Head
FKカラー 胸=FK Color: Chest
FKカラー 体=FK Color: Body
FKカラー 右手=FK Color: Right Hand
FKカラー 左手=FK Color: Left Hand
FKカラー スカート=FK Color: Skirt
ラインの描画=Draw Lines
アイテムFKのカラー設定=Item FK Color Settings
FKカラー アイテム=FK Color: Items
// Patch 0727
ルートシステム=Route System
ラインの幅=Line Width
ポイントの上限=Show Endpoint
あり=Show
// Patch 0831?
起動時シーンロード画面を開く=Open Scene Load Screen At Startup
開く=Open
開かない=Don't Open

// System Menu -> Logo
// Trivia: For awhile, this was the only part of the Studio an early version of the DynamicTranslation plugin could ever touch.
デフォルト=Default
illusion ロゴ 白=Illusion Logo (White)
illusion ロゴ 黒=Illusion Logo (Black)
illusion文字のみ 白=Word: Illusion (White)
illusion文字のみ 黒=Word: Illusion (Black)
キャラスタジオ 白=Character Studio (White)
キャラスタジオ 黒=Character Studio (Black)
キャラスタジオ ロゴ=Character Studio Logo
キャラスタジオ文字のみ 白=Words: "Character Studio" (White)
キャラスタジオ文字のみ 黒=Words: "Character Studio" (Black)
スタジオNEO V2 黒=Studio NEO V2 (Black)
スタジオNEO V2 白=Studio NEO V2 (White)
スタジオNEO V2 ロゴ=Studio NEO V2 Logo
スタジオNEO V2 文字のみ 白=Words: "Studio NEO V2" (White)
スタジオNEO V2 文字のみ 黒=Words: "Studio NEO V2" (Black)

背景 空=Background/Sky
晴れた空=Sunny Sky
夜空=Night Sky
夕暮れの空=Evening Sky
雨空=Rainy Sky
曇り空=Cloudy Sky

衣装=Outfit
トップス=Tops
ボトムス=Bottom
インナー上=Inner Top
インナー下=Inner Bottom
手袋=Gloves
パンスト=Pantyhose
靴下=Socks
靴=Shoes


// System Menu -> Character Lighting

// TODO: see if these are covered by RedirectedResources\assets\abdata\studio\info\*\light_*
カラー=Color
セルフシャドウ=Self-Shadowing
強さ=Strength
ライト縦=Vertical Light
ライト横=Horizontal Light

// System Menu -> Scene Effects
カラーの調整=Color Adjustment
色味=Shade
画面の色味調整=Scene Shade Adjustment
アンビエントオクルージョン=Ambient Occlusion
描画=Display
範囲=Intensity
被写界深度=Depth Of Field
焦点幅=Focus Distance
絞り=Aperture
ビネット=Vignette
フォグ=Fog
濃さ=Thickness
開始距離=Starting Distance
ブルーム=Bloom
しきい値=Threshold Level
光のぼかし=Light Gradation
サンシャフト=Sunshafts
しきい色=Threshold Color
光の色=Light Color
サンシャフト しきい色=Sunshafts: Threshold Color
サンシャフト 光の色=Sunshafts: Light Color
光源=Light Source
影の表現=Shadow Type
影の濃さ=Shadow Density
アウトラインの濃さ=Outline Density
アウトラインの幅=Outline Thickness
全体の陰影の色=Overall Shadow Color
// System Menu -> Scene Effects (New in AIS)
発光範囲\n=Emission range\n
発光上限=Emission upper limit
地平線=Horizon
拡散範囲=Diffusion range
影響度=Impact
画像サイズ=Image size
映り込み=Reflection
コントラスト=Contrast
環境光=Ambient light
光の範囲=Light range
色調整=Color adjustment
主被写体=Main subject
背景フォグ無効=Disable background fog
光の伸び=Light stretch
高さ濃度=Height concentration
スクリーンスペースリフレクション=Screen space reflection

// System Menu -> Scene Effects -> Color Adjustment -> Shade Dropdown List
昼=Midday
夕=Evening
夜=Night
//Live Concert Stage
ビビッド=Vivid
ノスタルジー=Nostalgia
レトロ=Retro
フラットカラー=Flat Color
フォースカラー=False Color
コミック=Comics
モノクロ=Monochrome
セピア=Sepia
リバースモノクロ=Reverse Monochrome
リバースカラー=Reverse Color
レッドアクセント=Red Accents
ブルーアクセント=Blue Accents
グリーンアクセント=Green Accents
スモッグ=Smog
ツートン=Two-Tone
アーティスティック=Artistic
ダークグレイッシュ=Dark Grayish
ライトシャドウ=Light Shadow
アンティーク=Antique
オールドポスター=Old Poster
ポスタライズ=Posterize
サーモグラフ=Thermograph
緑単色=Green Single Color
青単色=Blue Single Color
赤単色=Red Single Color
ブライト=Bright
ディープ=Deep
ストロング=Strong
ソフト=Soft
ペール=Pale
ダル=Sunset
寒色=Cold Color
暖色=Warm Color

// System Menu -> Scene Effects -> Light Source
マップ依存=Map Dependent

// System Menu -> Scene Effects -> Shadow Types
浅いなめらか影=Slight, Smooth Shadows
浅いくっきり影=Slight, Sharp Shadows
深いなめらか影=Deep, Smooth Shadows
深いくっきり影=Deep, Sharp Shadows
境界線のある影=Boundary Line Shadows
完全なめらか影=Perfect, Smooth Shadows
影なし=No Shadows

// System Menu -> Scene Effects -> Reflection
上からの光=Light From Above
暖かい光=Warm Light
不気味な光=Spooky Light
黄色い研究室=Yellow Laboratory
赤い研究室=Red Laboratory
青い研究室=Blue Laboratory
豪奢な空間=A Luxurious Space
日の差す部屋=Sunlit Room
部屋の中=In The Room
雪景色=Snow
海辺=Seaside
崖の上=On The Cliff
森の中=In The Forest

//
// Unsorted
//

彩度=Saturation
明度=Brightness
高さ=Height
滑らかさ=Smoothness
質感=Texture
色=Color
画像=Image
数値入力=Numeric Input
回転=Rotation
発光色=Light Color
発光の強さ=Light Strength
画面効果=Screen Effect
その他=Miscellaneous
地面=Ground
空=Sky


//
// object variation regexes
//
sr:"^(.*)\(キャラ\)$"=$1 (Character)
sr:"^(.*)\(マップ\)$"=$1 (Map)
sr:"^(.*)\(大\)$"=$1 (Large)
sr:"^(.*)\(中\)$"=$1 (Medium)
sr:"^(.*)\(小\)$"=$1 (Small)
sr:"^(.*)\(透明\)$"=$1 (Transparent)
sr:"^(.*)\(近\)$"=$1 (Near)
sr:"^(.*)\(遠\)$"=$1 (Far)
sr:"^(.*)\(obj無し\)$"=$1 (Without Object)
sr:"^(.*)\(アルファ\)$"=$1 (Alpha)
sr:"^(.*)\(通常\)$"=$1 (General)
