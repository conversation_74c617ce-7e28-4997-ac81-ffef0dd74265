# Traducción al Español para Room Girl

## Descripción
Esta es una traducción al español para el juego Room Girl. La traducción se aplica mientras el juego está ejecutándose y no requiere reemplazar o modificar ningún archivo del juego.

## Instalación
La traducción ya está instalada y configurada. El sistema de traducción automática está configurado para traducir del japonés al español.

## Configuración Actual
- **Idioma de destino**: Español (es)
- **Idioma de origen**: Japonés (ja)
- **Motor de traducción**: Google Translate V2
- **Traducción automática**: Habilitada
- **Traducción de texturas**: Habilitada

## Archivos de Traducción Incluidos

### Interfaz Principal
- `MainUI/Generic.txt` - Textos genéricos usados en múltiples escenas
- `MainUI/Options.txt` - Menú de opciones
- `MainUI/MainMenu.txt` - <PERSON><PERSON> principal
- `_AutoGeneratedTranslations.txt` - Traducciones generadas automáticamente

### Posiciones H
- `H-Positions/Insert.txt` - Posiciones de inserción
- `H-Positions/Service.txt` - Posiciones de servicio
- `H-Positions/Caress.txt` - Posiciones de caricias
- Y más...

### Creador de Personajes
- `CharacterMaker/` - Traducciones para el editor de personajes

### Estudio
- `Studio/` - Traducciones para Room Studio

## Cómo Funciona
1. El plugin XUnity AutoTranslator detecta texto en japonés
2. Busca traducciones manuales en los archivos .txt
3. Si no encuentra traducción manual, usa Google Translate automáticamente
4. Las traducciones automáticas se guardan en `_AutoGeneratedTranslations.txt`

## Personalización
Puedes editar cualquier archivo .txt en la carpeta `BepInEx/Translation/es/Text/` para:
- Corregir traducciones automáticas
- Añadir traducciones manuales
- Mejorar traducciones existentes

### Formato de Traducción
Cada línea sigue el formato:
```
texto_japonés=traducción_española
```

Ejemplo:
```
はい=Sí
いいえ=No
```

### Comentarios
Las líneas que comienzan con `//` son comentarios y no se procesan.

## Niveles de Alcance (Scope Levels)
El sistema soporta traducciones específicas por contexto:

- **Nivel -1**: Global
- **Nivel 3**: Pantalla de título
- **Nivel 4**: Creador de personajes
- **Nivel 5**: Mapa mundial
- **Nivel 6**: Escena de acción (UI de habitación)
- **Nivel 7**: Free H
- **Nivel 8**: Verificación de red
- **Nivel 9**: Subidor
- **Nivel 10**: Descargador
- **Nivel 11**: Entrada de red

## Controles Útiles
- **Ctrl+Alt+NP7**: Ver niveles de alcance cargados
- **F3**: Ayuda del juego (si está disponible)

## Solución de Problemas

### Si no aparecen las traducciones:
1. Verifica que el archivo `AutoTranslatorConfig.ini` tenga `Language=es`
2. Asegúrate de que `EnableUGUI=True` y `EnableTextMeshPro=True`
3. Reinicia el juego

### Si las traducciones están en inglés:
1. Verifica que los archivos estén en `BepInEx/Translation/es/Text/`
2. Comprueba que el formato de los archivos sea correcto
3. Asegúrate de que no haya caracteres especiales que rompan el formato

## Contribuir
Para mejorar las traducciones:
1. Edita los archivos .txt correspondientes
2. Usa traducciones naturales y apropiadas para el contexto
3. Mantén la consistencia en términos técnicos
4. Prueba las traducciones en el juego

## Estado de la Traducción
- ✅ Interfaz básica
- ✅ Menús principales
- ✅ Opciones
- ✅ Posiciones H básicas
- 🔄 Diálogos (traducción automática)
- 🔄 Subtítulos H (traducción automática)
- 🔄 Creador de personajes (parcial)
- 🔄 Studio (parcial)

## Notas
- Esta es una traducción inicial que se irá mejorando con el tiempo
- Las traducciones automáticas pueden no ser perfectas
- Se recomienda revisar y corregir las traducciones automáticas
- Los archivos de traducción se pueden editar mientras el juego está ejecutándose
