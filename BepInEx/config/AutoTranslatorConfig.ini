[Service]
Endpoint=GoogleTranslateV2
FallbackEndpoint=

[General]
Language=es
FromLanguage=ja

[Files]
Directory=Translation\{Lang}\Text
OutputFile=Translation\{Lang}\Text\_AutoGeneratedTranslations.txt
SubstitutionFile=Translation\{Lang}\Text\_Substitutions.txt
PreprocessorsFile=Translation\{Lang}\Text\_Preprocessors.txt
PostprocessorsFile=Translation\{Lang}\Text\_Postprocessors.txt

[TextFrameworks]
EnableIMGUI=False
EnableUGUI=True
EnableNGUI=False
EnableTextMeshPro=True
EnableTextMesh=False
EnableFairyGUI=False

[Behaviour]
MaxCharactersPerTranslation=200
IgnoreWhitespaceInDialogue=True
MinDialogueChars=20
ForceSplitTextAfterCharacters=0
CopyToClipboard=False
MaxClipboardCopyCharacters=2500
ClipboardDebounceTime=1.25
EnableUIResizing=True
EnableBatching=True
UseStaticTranslations=True
OverrideFont=
OverrideFontSize=
OverrideFontTextMeshPro=
FallbackFontTextMeshPro=
ResizeUILineSpacingScale=
ForceUIResizing=False
IgnoreTextStartingWith=\u180e;
TextGetterCompatibilityMode=False
GameLogTextPaths=
RomajiPostProcessing=ReplaceMacronWithCircumflex;RemoveApostrophes;ReplaceHtmlEntities;ReplaceWideCharacters
TranslationPostProcessing=ReplaceMacronWithCircumflex;ReplaceHtmlEntities;ReplaceWideCharacters
RegexPostProcessing=ReplaceWideCharacters
CacheRegexPatternResults=False
CacheRegexLookups=False
CacheWhitespaceDifferences=False
GenerateStaticSubstitutionTranslations=False
GeneratePartialTranslations=False
EnableTranslationScoping=True
EnableSilentMode=True
BlacklistedIMGUIPlugins=
EnableTextPathLogging=False
OutputUntranslatableText=False
IgnoreVirtualTextSetterCallingRules=False
MaxTextParserRecursion=3
HtmlEntityPreprocessing=True
HandleRichText=True
EnableTranslationHelper=False
ForceMonoModHooks=False
InitializeHarmonyDetourBridge=False
RedirectedResourceDetectionStrategy=AppendMongolianVowelSeparatorAndRemoveAll
OutputTooLongText=False
TemplateAllNumberAway=False
ReloadTranslationsOnFileChange=False
DisableTextMeshProScrollInEffects=False
CacheParsedTranslations=False

[Texture]
TextureDirectory=Translation\{Lang}\Texture
EnableTextureTranslation=True
EnableTextureDumping=False
EnableTextureToggling=False
EnableTextureScanOnSceneLoad=False
EnableSpriteRendererHooking=False
LoadUnmodifiedTextures=False
DetectDuplicateTextureNames=False
DuplicateTextureNames=
EnableLegacyTextureLoading=False
TextureHashGenerationStrategy=FromImageName
CacheTexturesInMemory=True
EnableSpriteHooking=True

[ResourceRedirector]
PreferredStoragePath=Translation\{Lang}\RedirectedResources
EnableTextAssetRedirector=False
LogAllLoadedResources=False
EnableDumping=False
CacheMetadataForAllFiles=True

[Http]
UserAgent=
DisableCertificateValidation=True

[TranslationAggregator]
Width=400
Height=100
EnabledTranslators=

[Debug]
EnableConsole=False

[Migrations]
Enable=True
Tag=5.1.0

[Baidu]
BaiduAppId=
BaiduAppSecret=
DelaySeconds=1

[BingLegitimate]
OcpApimSubscriptionKey=

[Custom]
Url=

[DeepL]
ExecutableLocation=
MinDelaySeconds=2
MaxDelaySeconds=6

[DeepLLegitimate]
ExecutableLocation=
ApiKey=
Free=False

[ezTrans]
InstallationPath=

[Google]
ServiceUrl=

[GoogleV2]
ServiceUrl=
RPCID=MkEWBc
VERSION=boq_translate-webserver_20210323.10_p0
UseSimplest=False

[GoogleLegitimate]
GoogleAPIKey=

[LecPowerTranslator15]
InstallationPath=

[Watson]
Url=
Key=

[Yandex]
YandexAPIKey=
