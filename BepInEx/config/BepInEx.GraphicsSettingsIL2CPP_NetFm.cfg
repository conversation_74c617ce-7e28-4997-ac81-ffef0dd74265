## Settings file was created by plugin Graphics Settings v0.7.0
## Plugin GUID: BepInEx.GraphicsSettingsIL2CPP_NetFm

[Apply on Startup]

## Apply graphics settings when you start the game. May also force the resolution when the game is running
# Setting type: Boolean
# Default value: false
Apply on Startup = false

[Framerate]

# Setting type: vSyncList
# Default value: On
# Acceptable values: Off, On, Half
vSync = On

## Target Framerate only works if vSync is Off. Set -1 to unlimited
# Setting type: Int32
# Default value: -1
Target Framerate = -1

[Resolution]

## Set Resolution Width. Minimum is 800
# Setting type: Int32
# Default value: 1280
Width = 1280

## Set Resolution Height. Minimum is 600
# Setting type: Int32
# Default value: 720
Height = 720

# Setting type: DisplayModeList
# Default value: Windowed
# Acceptable values: FullScreen, Windowed, Borderless_FullScreen
Display Mode = Windowed

