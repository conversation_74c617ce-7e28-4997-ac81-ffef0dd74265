﻿[Caching]

## Enable/disable assembly metadata cache
## Enabling this will speed up discovery of plugins and patchers by caching the metadata of all types BepInEx discovers.
# Setting type: Boolean
# Default value: true
EnableAssemblyCache = true

[Harmony.Logger]

## Specifies which Harmony log channels to listen to.
## NOTE: IL channel dumps the whole patch methods, use only when needed!
# Setting type: LogChannel
# Default value: Warn, <PERSON>rror
# Acceptable values: None, Info, IL, Warn, Error, Debug, All
# Multiple values can be set at the same time by separating them with , (e.g. Debug, Warning)
LogChannels = Warn, Error

[IL2CPP]

## Unity version to report to Il2CppUnhollower. If empty, version is automatically determined from the game process.
# Setting type: String
# Default value: 
UnityVersion = 

## Whether to run Il2CppAssemblyUnhollower automatically to generate Il2Cpp support assemblies when they are outdated.
## If disabled assemblies in `BepInEx/unhollowed` won't be updated between game or BepInEx updates!
## 
# Setting type: Boolean
# Default value: true
UpdateUnhollowedAssemblies = true

## URL to the ZIP of managed Unity base libraries.
## The base libraries are used by Il2CppUnhollower to generate unhollowed Unity assemblies
## The URL template MUST use HTTP.
## The URL can include {VERSION} template which will be replaced with the game's Unity engine version
## 
# Setting type: String
# Default value: http://unity.bepinex.dev/libraries/{VERSION}.zip
UnityBaseLibrariesSource = http://unity.bepinex.dev/libraries/{VERSION}.zip

## The IL2CPP metadata dumper tool to use when generating dummy assemblies for Il2CppAssemblyUnhollower.
## Il2CppDumper - Default. The traditional choice that has been used by BepInEx.
## Cpp2IL - Experimental, may provide better results than Il2CppDumper. Required for use with BepInEx.MelonLoader.Loader.
## 
# Setting type: IL2CPPDumperType
# Default value: Cpp2IL
# Acceptable values: Il2CppDumper, Cpp2IL
Il2CppDumperType = Cpp2IL

## If enabled, BepInEx will save dummy assemblies generated by an il2cpp dumper into BepInEx/dummy.
# Setting type: Boolean
# Default value: false
DumpDummyAssemblies = false

## The path to the folder where unhollowed assemblies are stored.
## Supports the following placeholders:
## {BepInEx} - Path to the BepInEx folder.
## {ProcessName} - Name of the current process
## 
# Setting type: String
# Default value: {BepInEx}
UnhollowedAssembliesPath = {BepInEx}/{ProcessName}

## The RegEx string to pass to Il2CppAssemblyUnhollower for renaming obfuscated names.
## All types and members matching this RegEx will get a name based on their signature,
## resulting in names that persist after game updates.
## 
# Setting type: String
# Default value: 
UnhollowerDeobfuscationRegex = 

[Logging]

## Enables showing unity log messages in the BepInEx logging system.
# Setting type: Boolean
# Default value: true
UnityLogListening = true

[Logging.Console]

## Enables showing a console for log output.
# Setting type: Boolean
# Default value: false
Enabled = false

## If enabled, will prevent closing the console (either by deleting the close button or in other platform-specific way).
# Setting type: Boolean
# Default value: false
PreventClose = false

## If true, console is set to the Shift-JIS encoding, otherwise UTF-8 encoding.
# Setting type: Boolean
# Default value: false
ShiftJisEncoding = false

## Hints console manager on what handle to assign as StandardOut. Possible values:
## Auto - lets BepInEx decide how to redirect console output
## ConsoleOut - prefer redirecting to console output; if possible, closes original standard output
## StandardOut - prefer redirecting to standard output; if possible, closes console out
## 
# Setting type: ConsoleOutRedirectType
# Default value: Auto
# Acceptable values: Auto, ConsoleOut, StandardOut
StandardOutType = Auto

## Which log levels to show in the console output.
# Setting type: LogLevel
# Default value: Fatal, Error, Warning, Message, Info
# Acceptable values: None, Fatal, Error, Warning, Message, Info, Debug, All
# Multiple values can be set at the same time by separating them with , (e.g. Debug, Warning)
LogLevels = All

[Logging.Disk]

## Include unity log messages in log file output.
# Setting type: Boolean
# Default value: false
WriteUnityLog = false

## Appends to the log file instead of overwriting, on game startup.
# Setting type: Boolean
# Default value: false
AppendLog = false

## Enables writing log messages to disk.
# Setting type: Boolean
# Default value: true
Enabled = true

## Only displays the specified log levels in the disk log output.
# Setting type: LogLevel
# Default value: Fatal, Error, Warning, Message, Info
# Acceptable values: None, Fatal, Error, Warning, Message, Info, Debug, All
# Multiple values can be set at the same time by separating them with , (e.g. Debug, Warning)
LogLevels = Fatal, Error, Warning, Message, Info

## If true, instantly writes any received log entries to disk.
## This incurs a major performance hit if a lot of log messages are being written, however it is really useful for debugging crashes.
## 
# Setting type: Boolean
# Default value: false
InstantFlushing = false

## The maximum amount of concurrent log files that will be written to disk.
## As one log file is used per open game instance, you may find it necessary to increase this limit when debugging multiple instances at the same time.
## 
# Setting type: Int32
# Default value: 5
ConcurrentFileLimit = 5

[Preloader]

## Specifies which MonoMod backend to use for Harmony patches. Auto uses the best available backend.
## This setting should only be used for development purposes (e.g. debugging in dnSpy). Other code might override this setting.
# Setting type: MonoModBackend
# Default value: auto
# Acceptable values: auto, dynamicmethod, methodbuilder, cecil
HarmonyBackend = auto

## If enabled, BepInEx will save patched assemblies into BepInEx/DumpedAssemblies.
## This can be used by developers to inspect and debug preloader patchers.
# Setting type: Boolean
# Default value: false
DumpAssemblies = false

## If enabled, BepInEx will load patched assemblies from BepInEx/DumpedAssemblies instead of memory.
## This can be used to be able to load patched assemblies into debuggers like dnSpy.
## If set to true, will override DumpAssemblies.
# Setting type: Boolean
# Default value: false
LoadDumpedAssemblies = false

## If enabled, BepInEx will call Debugger.Break() once before loading patched assemblies.
## This can be used with debuggers like dnSpy to install breakpoints into patched assemblies before they are loaded.
# Setting type: Boolean
# Default value: false
BreakBeforeLoadAssemblies = false

