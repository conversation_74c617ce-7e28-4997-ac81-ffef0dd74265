<?xml version="1.0"?>
<doc>
    <assembly>
        <name>BepInEx.Preloader.Unity</name>
    </assembly>
    <members>
        <member name="M:BepInEx.Preloader.Unity.DoorstopEntrypoint.Main">
            <summary>
                The main entrypoint of BepInEx, called from Doorstop.
            </summary>
        </member>
        <member name="T:BepInEx.Preloader.Unity.UnityPreloader">
            <summary>
                The main entrypoint of BepInEx, and initializes all patchers and the chainloader.
            </summary>
        </member>
        <member name="P:BepInEx.Preloader.Unity.UnityPreloader.PreloaderLog">
            <summary>
                The log writer that is specific to the preloader.
            </summary>
        </member>
        <member name="M:BepInEx.Preloader.Unity.UnityPreloader.AllocateConsole">
            <summary>
                Allocates a console window for use by BepInEx safely.
            </summary>
        </member>
        <member name="T:BepInEx.Preloader.RuntimeFixes.TraceFix">
            <summary>
                This exists because the Mono implementation of <see cref="T:System.Diagnostics.Trace" /> is/was broken, and would call Write directly
                instead of calling TraceEvent.
            </summary>
        </member>
        <member name="T:BepInEx.Shared.BuildInfoAttribute">
            <summary>
                This class is appended to AssemblyInfo.cs when BepInEx is built via a CI pipeline.
                It is mainly intended to signify that the current build is not a release build and is special, like for instance a
                bleeding edge build.
            </summary>
        </member>
    </members>
</doc>
