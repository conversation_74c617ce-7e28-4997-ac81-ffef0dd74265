<?xml version="1.0"?>
<doc>
    <assembly>
        <name>RG_Sideloader</name>
    </assembly>
    <members>
        <member name="T:Sideloader.Sideloader">
            <summary>
            Allows for loading mods in .zip format from the mods folder and automatically resolves ID conflicts.
            </summary>
        </member>
        <member name="M:Sideloader.Sideloader.Load">
            <inheritdoc/>
        </member>
        <member name="F:Sideloader.Sideloader.GUID">
            <summary> Plugin GUID </summary>
        </member>
        <member name="F:Sideloader.Sideloader.PluginName">
            <summary> Plugin name </summary>
        </member>
        <member name="F:Sideloader.Sideloader.Version">
            <summary> Plugin version </summary>
        </member>
        <member name="P:Sideloader.Sideloader.CommonDirectory">
            <inheritdoc/>
        </member>
        <member name="P:Sideloader.Sideloader.ModsDirectory">
            <summary> Directory from which to load mods </summary>
        </member>
        <member name="P:Sideloader.Sideloader.AbdataDirectory">
            <inheritdoc/>
        </member>
        <member name="P:Sideloader.Sideloader.UserDataDirectory">
            <inheritdoc/>
        </member>
        <member name="F:Sideloader.Sideloader.Zipmods">
            <summary> Dictionary of loaded zip file name and its zipmod metadata </summary>
        </member>
        <member name="F:Sideloader.Sideloader.Manifests">
            <summary> Dictionary of GUID and loaded manifest files </summary>
        </member>
        <member name="F:Sideloader.Sideloader.ZipArchives">
            <summary> Dictionary of GUID and loaded zip file name </summary>
        </member>
        <member name="F:Sideloader.Sideloader.LoadedManifests">
            <summary> List of all loaded manifest files </summary>
        </member>
        <member name="M:Sideloader.Sideloader.BuildPngFolderList(Sideloader.ZipmodInfo)">
            <summary>
            Construct a list of all folders that contain a .png
            </summary>
        </member>
        <member name="M:Sideloader.Sideloader.BuildPngOnlyFolderList">
            <summary>
            Build a list of folders that contain .pngs but do not match an existing asset bundle
            </summary>
        </member>
        <member name="M:Sideloader.Sideloader.IsPngFolderOnly(System.String)">
            <summary>
            Check whether the asset bundle matches a folder that contains .png files and does not match an existing asset bundle
            </summary>
        </member>
        <member name="M:Sideloader.Sideloader.IsModLoaded(System.String)">
            <summary>
            Check if a mod with specified GUID has been loaded.
            </summary>
        </member>
        <member name="M:Sideloader.Sideloader.GetManifest(System.String)">
            <summary>
            Check if a mod with specified GUID has been loaded and fetch its manifest.
            Returns null if there was no mod with this guid loaded.
            </summary>
            <param name="guid">GUID of the mod.</param>
            <returns>Manifest of the loaded mod or null if mod is not loaded.</returns>
        </member>
        <member name="M:Sideloader.Sideloader.GetPngNames">
            <summary>
            Get a list of file paths to all png files inside the loaded mods
            </summary>
        </member>
        <member name="M:Sideloader.Sideloader.GetPng(System.String,UnityEngine.TextureFormat,System.Boolean)">
            <summary>
            Get a new copy of the png file if it exists in any of the loaded zipmods
            </summary>
        </member>
        <member name="M:Sideloader.Sideloader.IsPng(System.String)">
            <summary>
            Check whether the .png file comes from a sideloader mod
            </summary>
        </member>
        <member name="M:Sideloader.Sideloader.TryGetExcelData(System.String,System.String,Illusion.Unity.Excel.ExcelData@)">
            <summary>
            Try to get ExcelData that was originally in .csv form in the mod
            </summary>
            <param name="assetBundleName">Name of the folder containing the .csv file</param>
            <param name="assetName">Name of the .csv file without the file extension</param>
            <param name="excelData">ExcelData or null if none exists</param>
            <returns>True if ExcelData was returned</returns>
        </member>
        <member name="M:Sideloader.Sideloader.IsSideloaderAB(System.String)">
            <summary>
            Check whether the asset bundle at the specified path is one managed by Sideloader
            </summary>
            <param name="assetBundlePath">Path to the asset bundle without the leading abdata, i.e. map/list/mapinfo/mymap.unity3d</param>
            <returns>True if the asset bundle is managed by Sideloader, false if not (doesn't exist, vanilla asset bundle, etc)</returns>
        </member>
        <member name="T:Sideloader.AutoResolver.MigrationInfo">
            <summary>
            Data about the migration to be performed
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.MigrationInfo.MigrationType">
            <summary>
            Type of migration to perform
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.MigrationInfo.Category">
            <summary>
            Category of the item
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.MigrationInfo.GUIDOld">
            <summary>
            GUID of the item to perform migration on
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.MigrationInfo.GUIDNew">
            <summary>
            GUID to migrate to
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.MigrationInfo.IDOld">
            <summary>
            ID of the item to perform migration on
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.MigrationInfo.IDNew">
            <summary>
            ID to migrate to
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.MigrationInfo.#ctor(Sideloader.AutoResolver.MigrationType,Chara.ChaListDefine.CategoryNo,System.String,System.String,System.Int32,System.Int32)">
            <inheritdoc/>
        </member>
        <member name="M:Sideloader.AutoResolver.MigrationInfo.#ctor(Sideloader.AutoResolver.MigrationType,System.String,System.String)">
            <inheritdoc/>
        </member>
        <member name="T:Sideloader.AutoResolver.UniversalAutoResolver">
            <summary>
            Automatically resolves ID conflicts by saving GUID to the card and changing item IDs at runtime
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.UniversalAutoResolver.UARExtID">
            <summary>
            Extended save ID
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.UniversalAutoResolver.UARExtIDOld">
            <summary>
            Extended save ID used in EmotionCreators once upon a time, no longer used but must still be checked for cards that still use it
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.UniversalAutoResolver.BaseSlotID">
            <summary>
            The starting point for UAR IDs
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.GetUniqueSlotID">
            <summary>
            Get a new unique slot ID above <see cref="F:Sideloader.AutoResolver.UniversalAutoResolver.BaseSlotID"/>. Returns a different unique ID on every call.
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.UniversalAutoResolver.LoadedResolutionInfo">
            <summary>
            All loaded ResolveInfos.
            Use TryGetResolutionInfo if you need to find a specific item since it's much faster.
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.TryGetResolutionInfo(System.String,System.Int32)">
            <summary>
            Get the ResolveInfo for an item
            </summary>
            <param name="property">Property as defined in StructReference</param>
            <param name="localSlot">Current (resolved) ID of the item</param>
            <returns>ResolveInfo</returns>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.TryGetResolutionInfo(Chara.ChaListDefine.CategoryNo,System.Int32)">
            <summary>
            Get the ResolveInfo for an item
            </summary>
            <param name="categoryNo">Category number of the item</param>
            <param name="localSlot">Current (resolved) ID of the item</param>
            <returns>ResolveInfo</returns>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.TryGetResolutionInfo(System.Int32,System.String,Chara.ChaListDefine.CategoryNo)">
            <summary>
            Get the ResolveInfo for an item. Used for compatibility resolving in cases where GUID is not known (hard mods).
            </summary>
            <param name="slot">Original ID as defined in the list file</param>
            <param name="property">Property as defined in StructReference</param>
            <param name="categoryNo">Category number of the item</param>
            <returns>ResolveInfo</returns>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.TryGetResolutionInfo(System.Int32,Chara.ChaListDefine.CategoryNo,System.String)">
            <summary>
            Get the ResolveInfo for an item
            </summary>
            <param name="slot">Original ID as defined in the list file</param>
            <param name="categoryNo">Category number of the item</param>
            <param name="guid"></param>
            <returns>ResolveInfo</returns>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.TryGetResolutionInfo(System.Int32,System.String,System.String)">
            <summary>
            Get the ResolveInfo for an item
            </summary>
            <param name="slot">Original ID as defined in the list file</param>
            <param name="property"></param>
            <param name="guid"></param>
            <returns>ResolveInfo</returns>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.TryGetResolutionInfo(System.Int32,System.String,Chara.ChaListDefine.CategoryNo,System.String)">
            <summary>
            Get the ResolveInfo for an item
            </summary>
            <param name="slot">Original ID as defined in the list file</param>
            <param name="property"></param>
            <param name="categoryNo"></param>
            <param name="guid"></param>
            <returns>ResolveInfo</returns>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.GetMigrationInfo(System.String)">
            <summary>
            Get all MigrationInfo for the GUID
            </summary>
            <param name="guidOld">GUID that will be migrated</param>
            <returns>A list of MigrationInfo</returns>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.GetMigrationInfo(System.Int32)">
            <summary>
            Get all MigrationInfo for the ID
            </summary>
            <param name="idOld">ID that will be migrated</param>
            <returns>A list of MigrationInfo</returns>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.ResolveStructure(System.Collections.Generic.Dictionary{Sideloader.AutoResolver.CategoryProperty,Sideloader.AutoResolver.StructValue{System.Int32}},System.Object,System.Collections.Generic.ICollection{Sideloader.AutoResolver.ResolveInfo},System.String)">
            <summary>
            Change the ID of items saved to a card to their resolved IDs
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.UniversalAutoResolver.Hooks.DoingImport">
            <summary>
            A flag for disabling certain events when importing KK cards to EC. Should always be set to false in InstallHooks for KK and always remain false.
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.Hooks.LoadFacePresetPrefix(Chara.ChaFileControl,Sideloader.AutoResolver.HeadPresetInfo@)">
            <summary>
            Find the head preset data
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.Hooks.LoadFacePresetPostfix(Chara.ChaFileControl,Sideloader.AutoResolver.HeadPresetInfo@)">
            <summary>
            Use the head preset data to resolve the IDs
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.Hooks.SavePrefix">
            <summary>
            Before the scene saves, go through every item, map, BGM, etc. in the scene, create extended save data with the GUID and other relevant info,
            and restore the IDs back to the original, non-resolved ID for hard mod compatibility
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.Hooks.SavePostfix">
            <summary>
            Set item IDs back to the resolved ID
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.UniversalAutoResolver.UARExtIDStudioAnimation">
            <summary>
            Extended save ID for Studio animations saved to characters in scenes
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.UniversalAutoResolver.LoadedStudioResolutionInfo">
            <summary>
            All loaded StudioResolveInfo
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.UniversalAutoResolver.StudioResolutionInfoLocalSlotLookup">
            <summary>
            LocalSlot -> resolve info lookup.
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.UniversalAutoResolver.StudioResolutionInfoGuidLookup">
            <summary>
            GUID + Slot -> resolve info lookup.
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.AddStudioResolutionInfo(Sideloader.AutoResolver.StudioResolveInfo)">
            <inheritdoc/>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.UpdateLookupsIfNeeded">
            <summary>
            Handle other plugins adding items to LoadedStudioResolutionInfo directly. Assumes items are added to the end, not inserted.
            Necessary for AnimationLoader to work.
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.GetStudioResolveInfos(System.Int32,System.Boolean)">
            <summary>
            Get all resolve infos with a given Local Slot. Optionally only return ResolveItems.
            With <paramref name="onlyResolveItems"/><code>==true</code> in most cases returns a single item so it's fine to use FirstOrDefault.
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.GetStudioResolveInfos(System.String,System.Int32,System.Boolean)">
            <summary>
            Get all resolve infos with a given GUID and Slot. Optionally only return ResolveItems.
            With <paramref name="onlyResolveItems"/><code>==true</code> in most cases returns a single item so it's fine to use FirstOrDefault.
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.ResolveStudioObject(System.Object)">
            <summary>
            Compatibility resolving for objects with no extended save data
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.GetMapID">
            <summary>
            Get the current map's ID
            </summary>
            <returns></returns>
        </member>
        <member name="M:Sideloader.AutoResolver.UniversalAutoResolver.SetMapID(System.Int32)">
            <summary>
            Set the current map's ID
            </summary>
            <param name="id"></param>
        </member>
        <member name="T:Sideloader.AutoResolver.MigrationType">
            <summary>
            Type of migration that will be performed
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.MigrationType.Migrate">
            <summary>
            Change the old GUID to the new GUID and the old ID to the new ID
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.MigrationType.MigrateAll">
            <summary>
            Change the old GUID to the new GUID for all IDs
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.MigrationType.StripAll">
            <summary>
            Remove the GUID and perform compatibility resolve
            </summary>
        </member>
        <member name="T:Sideloader.AutoResolver.ResolveInfo">
            <summary>
            Contains information saved to the card for resolving ID conflicts
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.ResolveInfo.GUID">
            <summary>
            GUID of the mod as defined in the manifest.xml
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.ResolveInfo.Slot">
            <summary>
            ID of the item as defined in the mod's list files
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.ResolveInfo.LocalSlot">
            <summary>
            Resolved item ID. IDs greater than 100000000 are resolved IDs belonging to Sideloader. Use the resolved ID (local slot) to look up the original ID (slot)
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.ResolveInfo.Property">
            <summary>
            Property of the object as defined in Sideloader's StructReference.
            If ever you need to know what to use for this, enable debug resolve info logging and see what Sideloader generates at the start of the game.
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.ResolveInfo.CategoryNo">
            <summary>
            ChaListDefine.CategoryNo. Typically only used for hard mod resolving in cases where the GUID is not known.
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.ResolveInfo.Author">
            <summary>
            Author of the mod as defined in the manifest.xml
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.ResolveInfo.Website">
            <summary>
            Website of the mod as defined in the manifest.xml
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.ResolveInfo.Name">
            <summary>
            Display name of the mod as defined in the manifest.xml
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.StudioObjectSearch.FindObjectInfoOrder(Sideloader.AutoResolver.StudioObjectSearch.SearchType,System.Type)">
            <summary>
            Returns a dictionary of ObjectInfo.dicKey and their order in a scene for the specified ObjectInfo type.
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.StudioObjectSearch.FindObjectInfo(Sideloader.AutoResolver.StudioObjectSearch.SearchType)">
            <summary>
            Returns a dictionary of ObjectInfo.dicKey and ObjectInfo of every ObjectInfo in a scene
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.StudioObjectSearch.FindObjectInfoAndOrder(Sideloader.AutoResolver.StudioObjectSearch.SearchType,System.Type,System.Collections.Generic.Dictionary{System.Int32,System.Int32}@)">
            <summary>
            Returns a dictionary of ObjectInfo.dicKey and ObjectInfo of every ObjectInfo in a scene.
            Also a dictionary of ObjectInfo.dicKey and their order in a scene for the specified ObjectInfo type as an out parameter.
            </summary>
        </member>
        <member name="M:Sideloader.AutoResolver.StudioObjectSearch.FindObjectsRecursive(System.Object,System.Collections.Generic.Dictionary{System.Int32,System.Object}@,System.Collections.Generic.Dictionary{System.Int32,System.Int32}@,System.Int32@,System.Type)">
            <summary>
            Function for finding all ObjectInfo recursively
            </summary>
        </member>
        <member name="T:Sideloader.AutoResolver.StudioPatternResolveInfo">
            <summary>
            Contains information saved to the card for resolving ID conflicts
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioPatternResolveInfo.DicKey">
            <summary>
            Dictionary key of the item, used on scene load
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioPatternResolveInfo.ObjectOrder">
            <summary>
            Order of the item saved to the scene, used on scene import
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioPatternResolveInfo.ObjectPatternInfo">
            <summary>
            Information about the patterns saved to the item
            </summary>
        </member>
        <member name="T:Sideloader.AutoResolver.StudioPatternResolveInfo.PatternInfo">
            <summary>
            Information about the patterns
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.StudioPatternResolveInfo.PatternInfo.GUID">
            <summary>
            GUID of the mod as defined in the manifest.xml
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.StudioPatternResolveInfo.PatternInfo.Slot">
            <summary>
            ID of the item as defined in the mod's list files
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.StudioPatternResolveInfo.PatternInfo.LocalSlot">
            <summary>
            Resolved item ID. IDs greater than 100000000 are resolved IDs belonging to Sideloader. Use the resolved ID (local slot) to look up the original ID (slot)
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.StudioPatternResolveInfo.PatternInfo.Author">
            <summary>
            Author of the mod as defined in the manifest.xml
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.StudioPatternResolveInfo.PatternInfo.Website">
            <summary>
            Website of the mod as defined in the manifest.xml
            </summary>
        </member>
        <member name="F:Sideloader.AutoResolver.StudioPatternResolveInfo.PatternInfo.Name">
            <summary>
            Name of the mod as defined in the manifest.xml
            </summary>
        </member>
        <member name="T:Sideloader.AutoResolver.StudioResolveInfo">
            <summary>
            Contains information saved to the card for resolving ID conflicts
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.GUID">
            <summary>
            GUID of the mod as defined in the manifest.xml
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.Slot">
            <summary>
            ID of the item as defined in the mod's list files
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.LocalSlot">
            <summary>
            Resolved item ID. IDs greater than 100000000 are resolved IDs belonging to Sideloader. Use the resolved ID (local slot) to look up the original ID (slot)
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.DicKey">
            <summary>
            Dictionary key of the item, used on scene load
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.ObjectOrder">
            <summary>
            Order of the item saved to the scene, used on scene import
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.ResolveItem">
            <summary>
            Used to determine if the item should be searched for ID lookups
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.Group">
            <summary>
            Group of the item, used by animations
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.Category">
            <summary>
            Category of the item, used by animations
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.Author">
            <summary>
            Author of the mod as defined in the manifest.
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.Website">
            <summary>
            /// Author of the mod as defined in the manifest.
            </summary>
        </member>
        <member name="P:Sideloader.AutoResolver.StudioResolveInfo.Name">
            <summary>
            Display name of the mod as defined in the manifest.xml
            </summary>
        </member>
        <member name="T:Sideloader.Manifest">
            <summary>
            Contains data about the loaded manifest.xml
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.SchemaVer">
            <summary>
            Version of this manifest.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.GUID">
            <summary>
            GUID of the mod.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.Name">
            <summary>
            Name of the mod. Only used for display the name of the mod when mods are loaded.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.Version">
            <summary>
            Version of the mod.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.Author">
            <summary>
            Author of the mod. Not currently used for anything.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.Website">
            <summary>
            Website of the mod. Not currently used for anything.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.Description">
            <summary>
            Description of the mod. Not currently used for anything.
            </summary>
        </member>
        <member name="F:Sideloader.Manifest.manifestDocument">
            <summary>
            Parsed contents of the manifest.xml.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.ManifestDocument">
            <summary>
            Parsed contents of the manifest.xml.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.ManifestString">
            <summary>
            Raw contents of the manifest.xml.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.Game">
            <summary>
            Game the mod is made for. If specified, the mod will only load for that game. If not specified will load on any game.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.Games">
            <summary>
            Games the mod is made for. If specified, the mod will only load for those games. If not specified will load on any game.
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.MigrationList">
            <summary>
            List of all migration info for this mod
            </summary>
        </member>
        <member name="P:Sideloader.Manifest.HeadPresetList">
            <inheritdoc/>
        </member>
        <member name="P:Sideloader.Manifest.FaceSkinList">
            <inheritdoc/>
        </member>
        <member name="M:Sideloader.Manifest.#ctor(System.IO.Stream)">
            <inheritdoc/>
        </member>
        <member name="M:Sideloader.Manifest.#ctor(System.Int32,System.String,System.String,System.String,System.String,System.String,System.String,System.String,System.Collections.Generic.List{System.String},System.Collections.Generic.List{Sideloader.AutoResolver.MigrationInfo},System.Collections.Generic.List{Sideloader.AutoResolver.HeadPresetInfo},System.Collections.Generic.List{Sideloader.AutoResolver.FaceSkinInfo})">
            <inheritdoc/>
        </member>
        <member name="T:XUnity.ResourceRedirector.ResourceRedirectionIl2Cpp">
            <summary>
            Entrypoint to the resource redirection API.
            </summary>
        </member>
        <member name="M:XUnity.ResourceRedirector.ResourceRedirectionIl2Cpp.EnableRedirectMissingAssetBundlesToEmptyAssetBundle(System.Int32)">
            <summary>
            Creates an asset bundle hook that redirects asset bundles loads to an empty
            asset bundle if the file that is being loaded does not exist.
            </summary>
            <param name="priority">Priority of the hook.</param>
        </member>
        <member name="T:XUnity.ResourceRedirector.AssetBundleHelperIl2Cpp">
            <summary>
            Utility methods for AssetBundles.
            </summary>
        </member>
        <member name="M:XUnity.ResourceRedirector.AssetBundleHelperIl2Cpp.CreateEmptyAssetBundle">
            <summary>
            Creates an empty AssetBundle with a randomly generated CAB identifier.
            </summary>
            <returns>The empty asset bundle with a random CAB identifier.</returns>
        </member>
        <member name="M:XUnity.ResourceRedirector.AssetBundleHelperIl2Cpp.CreateEmptyAssetBundleRequest">
            <summary>
            Creates an empty AssetBundle request with a randomly generated CAB identifier.
            </summary>
            <returns>The asset bundle request with a random CAB identifier.</returns>
        </member>
        <member name="M:XUnity.ResourceRedirector.AssetBundleHelperIl2Cpp.LoadFromMemory(System.String,System.Byte[],System.UInt32)">
            <summary>
            Convenience method to maintain a name of an asset bundle being loaded through
            memory for logging purposes.
            </summary>
            <param name="path">Path to the asset bundle being loaded. Only used for logging.</param>
            <param name="binary">Binary data of the asset bundle being loaded.</param>
            <param name="crc">Crc of the asset bundle.</param>
            <returns>The loaded asset bundle.</returns>
        </member>
        <member name="M:XUnity.ResourceRedirector.AssetBundleHelperIl2Cpp.LoadFromMemoryAsync(System.String,System.Byte[],System.UInt32)">
            <summary>
            Convenience method to maintain a name of an asset bundle being loaded through
            memory for logging purposes.
            </summary>
            <param name="path">Path to the asset bundle being loaded. Only used for logging.</param>
            <param name="binary">Binary data of the asset bundle being loaded.</param>
            <param name="crc">Crc of the asset bundle.</param>
            <returns>The request.</returns>
        </member>
        <member name="M:XUnity.ResourceRedirector.AssetBundleHelperIl2Cpp.LoadFromFileWithRandomizedCabIfRequired(System.String,System.UInt32,System.UInt64)">
            <summary>
            Loads an asset bundle from a file. If loading fails, randomize the CAB and try again from memory.
            </summary>
            <param name="path"></param>
            <param name="crc"></param>
            <param name="offset"></param>
            <returns></returns>
        </member>
        <member name="F:BepisPlugins.Metadata.PluginsVersion">
            <summary>
            Version number used be all plugins. Must be convertible to <see cref="T:System.Version"/>
            Use ReleaseVersion.HotfixVersion format
            </summary>
        </member>
        <member name="M:Extensions.Replace(System.String,System.String,System.String,System.StringComparison)">
            <summary>
            Returns a new string in which all occurrences of a specified string in the current instance are replaced with another
            specified string according the type of search to use for the specified string.
            Stolen from https://stackoverflow.com/a/45756981
            </summary>
            <param name="str">The string performing the replace method.</param>
            <param name="oldValue">The string to be replaced.</param>
            <param name="newValue">The string replace all occurrences of <paramref name="oldValue"/>.
            If value is equal to <c>null</c>, than all occurrences of <paramref name="oldValue"/> will be removed from the <paramref name="str"/>.</param>
            <param name="comparisonType">One of the enumeration values that specifies the rules for the search.</param>
            <returns>A string that is equivalent to the current string except that all instances of <paramref name="oldValue"/> are replaced with <paramref name="newValue"/>.
            If <paramref name="oldValue"/> is not found in the current instance, the method returns the current instance unchanged.</returns>
        </member>
        <member name="M:Extensions.FindPosition(Il2CppSystem.IO.Stream,System.Byte[])">
            <summary>
            Find first position of the byte sequence in the stream starting at current position.
            Returns position of first byte of the sequence.
            </summary>
        </member>
    </members>
</doc>
