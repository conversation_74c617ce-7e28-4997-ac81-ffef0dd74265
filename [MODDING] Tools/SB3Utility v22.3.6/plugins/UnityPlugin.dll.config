<?xml version="1.0" encoding="utf-8"?>
<configuration>
    <configSections>
        <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
            <section name="UnityPlugin.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
        </sectionGroup>
    </configSections>
    <applicationSettings>
        <UnityPlugin.Properties.Settings>
            <setting name="KeepBackupOfUnity3d" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="BackupExtensionUnity3d" serializeAs="String">
                <value>.unit-y3d</value>
            </setting>
            <setting name="BackupExtensionAssets" serializeAs="String">
                <value>.asse-ts</value>
            </setting>
            <setting name="BackupExtensionNone" serializeAs="String">
                <value>.none</value>
            </setting>
            <setting name="DialogRendererMeshAttributesSize" serializeAs="String">
                <value>0, 0</value>
            </setting>
            <setting name="MonoBehaviourEditorSize" serializeAs="String">
                <value>0, 0</value>
            </setting>
            <setting name="DialogAnimationDragDropSize" serializeAs="String">
                <value>0, 0</value>
            </setting>
            <setting name="AnimationExportFormat" serializeAs="String">
                <value>FBX 2020.3.1</value>
            </setting>
            <setting name="FbxExportDisplayBoneSize" serializeAs="String">
                <value>-1</value>
            </setting>
            <setting name="DialogCameraSize" serializeAs="String">
                <value>0, 0</value>
            </setting>
            <setting name="ExportUncompressedAs" serializeAs="String">
                <value>BMP</value>
            </setting>
            <setting name="AssetBundleClearMainAsset" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="FbxExportFlatInBetween" serializeAs="String">
                <value>False</value>
            </setting>
            <setting name="ExportAnimationFilenamePattern" serializeAs="String">
                <value>{Clip}-{Slot}-{Animator}-</value>
            </setting>
            <setting name="DialogPasteIntoSize" serializeAs="String">
                <value>0, 0</value>
            </setting>
            <setting name="BC7asDXT5" serializeAs="String">
                <value>True</value>
            </setting>
            <setting name="BC7unflipped" serializeAs="String">
                <value>False</value>
            </setting>
        </UnityPlugin.Properties.Settings>
    </applicationSettings>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5"/></startup>
</configuration>
