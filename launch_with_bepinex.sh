#!/bin/bash

# Script para lanzar Room Girl con BepInEx en Linux
# Asegura que BepInEx se cargue correctamente

echo "=== Iniciando Room Girl con BepInEx ==="

# Cambiar al directorio del juego
cd "/home/<USER>/RoomGirl Paradise"

# Verificar archivos necesarios
echo "Verificando archivos necesarios..."

if [ ! -f "winhttp.dll" ]; then
    echo "ERROR: winhttp.dll no encontrado"
    exit 1
fi

if [ ! -f "BepInEx/core/BepInEx.IL2CPP.dll" ]; then
    echo "ERROR: BepInEx.IL2CPP.dll no encontrado"
    exit 1
fi

if [ ! -f "doorstop_config.ini" ]; then
    echo "ERROR: doorstop_config.ini no encontrado"
    exit 1
fi

echo "Todos los archivos necesarios están presentes."

# Configurar variables de entorno para Wine
export WINEDLLOVERRIDES="winhttp=n,b"
export WINEPREFIX="$HOME/.local/share/lutris/prefixes/room-girl"

# Si no existe el prefijo, usar el predeterminado
if [ ! -d "$WINEPREFIX" ]; then
    echo "Prefijo de Wine no encontrado, usando configuración predeterminada..."
    unset WINEPREFIX
fi

echo "Configuración de Wine:"
echo "WINEDLLOVERRIDES=$WINEDLLOVERRIDES"
echo "WINEPREFIX=$WINEPREFIX"

# Limpiar logs anteriores
rm -f BepInEx/LogOutput.log
rm -f output_log.txt

echo "Iniciando el juego..."

# Intentar lanzar con Wine directamente
wine RoomGirl.exe

echo "=== Juego cerrado ==="

# Mostrar información de debug
if [ -f "BepInEx/LogOutput.log" ]; then
    echo "=== Log de BepInEx encontrado ==="
    echo "Últimas 10 líneas:"
    tail -10 BepInEx/LogOutput.log
else
    echo "=== No se encontró log de BepInEx ==="
    echo "BepInEx no se cargó correctamente."
fi

if [ -f "output_log.txt" ]; then
    echo "=== Log de Unity encontrado ==="
    echo "Buscando menciones de BepInEx:"
    grep -i bepinex output_log.txt | tail -5
fi
