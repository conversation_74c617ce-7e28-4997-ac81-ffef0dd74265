#!/bin/bash

# Script para lanzar Room Girl con BepInEx en Linux
# Asegura que BepInEx se cargue correctamente

echo "=== Iniciando Room Girl con BepInEx ==="

# Cambiar al directorio del juego
cd "/home/<USER>/RoomGirl Paradise"

# Verificar archivos necesarios
echo "Verificando archivos necesarios..."

if [ ! -f "winhttp.dll" ]; then
    echo "ERROR: winhttp.dll no encontrado"
    exit 1
fi

if [ ! -f "BepInEx/core/BepInEx.IL2CPP.dll" ]; then
    echo "ERROR: BepInEx.IL2CPP.dll no encontrado"
    exit 1
fi

if [ ! -f "doorstop_config.ini" ]; then
    echo "ERROR: doorstop_config.ini no encontrado"
    exit 1
fi

echo "Todos los archivos necesarios están presentes."

# Buscar Wine de Lutris
LUTRIS_WINE=""
if [ -f "$HOME/.local/share/lutris/runners/wine/wine-ge-8-26-x86_64/bin/wine" ]; then
    LUTRIS_WINE="$HOME/.local/share/lutris/runners/wine/wine-ge-8-26-x86_64/bin/wine"
elif [ -f "$HOME/.local/share/lutris/runners/wine/GE-Proton10-3/files/bin/wine" ]; then
    LUTRIS_WINE="$HOME/.local/share/lutris/runners/wine/GE-Proton10-3/files/bin/wine"
else
    # Buscar cualquier Wine de Lutris
    LUTRIS_WINE=$(find ~/.local/share/lutris/runners/wine/ -name "wine" -type f 2>/dev/null | head -1)
fi

if [ -n "$LUTRIS_WINE" ]; then
    echo "Usando Wine de Lutris: $LUTRIS_WINE"
    WINE_CMD="$LUTRIS_WINE"
else
    echo "No se encontró Wine de Lutris, usando Wine del sistema..."
    WINE_CMD="wine"
fi

# Buscar prefijo de Wine correcto
WINE_PREFIX=""
POSSIBLE_PREFIXES=(
    "$HOME/.local/share/lutris/prefixes/room-girl"
    "$HOME/.local/share/lutris/prefixes/roomgirl"
    "$HOME/.local/share/lutris/prefixes/room_girl"
)

for prefix in "${POSSIBLE_PREFIXES[@]}"; do
    if [ -d "$prefix" ]; then
        WINE_PREFIX="$prefix"
        break
    fi
done

# Configurar variables de entorno para Wine
export WINEDLLOVERRIDES="winhttp=n,b"
if [ -n "$WINE_PREFIX" ]; then
    export WINEPREFIX="$WINE_PREFIX"
    echo "Usando prefijo de Wine: $WINE_PREFIX"
else
    echo "No se encontró prefijo específico, usando configuración predeterminada..."
fi

echo "Configuración de Wine:"
echo "WINE_CMD=$WINE_CMD"
echo "WINEDLLOVERRIDES=$WINEDLLOVERRIDES"
echo "WINEPREFIX=$WINEPREFIX"

# Limpiar logs anteriores
rm -f BepInEx/LogOutput.log
rm -f output_log.txt

echo "Iniciando el juego..."

# Intentar lanzar con Wine de Lutris
"$WINE_CMD" RoomGirl.exe

echo "=== Juego cerrado ==="

# Mostrar información de debug
if [ -f "BepInEx/LogOutput.log" ]; then
    echo "=== Log de BepInEx encontrado ==="
    echo "Últimas 10 líneas:"
    tail -10 BepInEx/LogOutput.log
else
    echo "=== No se encontró log de BepInEx ==="
    echo "BepInEx no se cargó correctamente."
fi

if [ -f "output_log.txt" ]; then
    echo "=== Log de Unity encontrado ==="
    echo "Buscando menciones de BepInEx:"
    grep -i bepinex output_log.txt | tail -5
fi
