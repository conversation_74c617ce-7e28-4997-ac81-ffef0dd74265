<linker>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.LegacyResourcesProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.Camera" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.MeshRenderer" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.MeshFilter" preserve="all" />
    <type fullname="UnityEngine.Mesh" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.Cubemap" preserve="all" />
    <type fullname="UnityEngine.Light" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.SpriteRenderer" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.PhysicsModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.BoxCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AudioListener" preserve="all" />
    <type fullname="UnityEngine.AudioSource" preserve="all" />
    <type fullname="UnityEngine.AudioReverbFilter" preserve="all" />
    <type fullname="UnityEngine.Audio.AudioMixer" preserve="all" />
    <type fullname="UnityEngine.Audio.AudioMixerGroup" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.AnimationModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Animator" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.UI.Toggle" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.Slider" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.InputField" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.Text" preserve="all" />
    <type fullname="UnityEngine.UI.ScrollRect" preserve="all" />
    <type fullname="UnityEngine.UI.Scrollbar" preserve="all" />
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.LayoutElement" preserve="all" />
    <type fullname="UnityEngine.UI.Button" preserve="all" />
    <type fullname="UnityEngine.UI.GridLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Outline" preserve="all" />
    <type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.EventSystems.TouchInputModule" preserve="all" />
    <type fullname="UnityEngine.UI.Shadow" preserve="all" />
    <type fullname="UnityEngine.EventSystems.EventSystem" preserve="all" />
    <type fullname="UnityEngine.UI.ToggleGroup" preserve="all" />
    <type fullname="UnityEngine.UI.ContentSizeFitter" preserve="all" />
    <type fullname="UnityEngine.EventSystems.PhysicsRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.RectMask2D" preserve="all" />
    <type fullname="UnityEngine.EventSystems.StandaloneInputModule" preserve="all" />
    <type fullname="UnityEngine.EventSystems.EventTrigger" preserve="all" />
    <type fullname="UnityEngine.EventSystems.EventTrigger/Entry" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.EventSystems.EventTrigger/TriggerEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.InputField/OnChangeEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.InputField/SubmitEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Scrollbar/ScrollEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.HighDefinition.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.HighDefinition.HDAdditionalCameraData" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.DepthOfField" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.AmbientOcclusion" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ColorAdjustments" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ChromaticAberration" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDAdditionalLightData" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.Exposure" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.Vignette" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.VisualEnvironment" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.Bloom" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDRISky" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.IndirectLightingController" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ScreenSpaceReflection" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.Fog" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ContactShadows" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.GlobalIllumination" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDShadowSettings" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ColorCurves" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.Tonemapping" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.MicroShadowing" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ShadowsMidtonesHighlights" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.AdaptationModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.BackplateTypeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.BloomResolutionParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.BoolScalableSettingValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CascadeEndBorderParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.CascadePartitionSplitParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.DepthOfFieldModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.DepthOfFieldResolutionParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.EnvUpdateParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ExposureModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FogColorParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FogControlParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FogDenoisingModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FogTypeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FrameSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FrameSettingsOverrideMask" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDPhysicalCamera" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.IndirectLightingController/LightLayerEnumParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.IntScalableSettingValue" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.LuminanceSourceParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.MeteringModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ObsoleteFrameSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ObsoleteLightLoopSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.RayTracingModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.SSRAlgoParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ScalableSettingLevelParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.SkyAmbientModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.SkyIntensityParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.TargetMidGrayParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.TonemappingModeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.VignetteModeParameter" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Assembly-CSharp, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="RG.UI.Animation2D" preserve="all" />
    <type fullname="RG.Config.TextSetting" preserve="all" />
    <type fullname="RG.Config.VoiceSetting" preserve="all" />
    <type fullname="RG.Config.HelpButtonUI" preserve="all" />
    <type fullname="ILSetUtility.TimeUtility" preserve="all" />
    <type fullname="ADV.BackLogViewer" preserve="all" />
    <type fullname="RG.Help.HelpWindowToggle" preserve="all" />
    <type fullname="RG.UI.DialogDataBlock" preserve="all" />
    <type fullname="RG.UI.DialogShopItem" preserve="all" />
    <type fullname="RG.Config.HSetting" preserve="all" />
    <type fullname="Illusion.Component.AutoLayoutCtrl" preserve="all" />
    <type fullname="Manager.HSceneManager" preserve="all" />
    <type fullname="TextController" preserve="all" />
    <type fullname="Config.SliderToText" preserve="all" />
    <type fullname="Manager.Game" preserve="all" />
    <type fullname="RG.ShortcutViewDialog" preserve="all" />
    <type fullname="Manager.Sound" preserve="all" />
    <type fullname="CameraEffector.ConfigEffectorVolume" preserve="all" />
    <type fullname="BeautifyHDRP.BeautifySettings" preserve="all" />
    <type fullname="RG.UI.DialogButton" preserve="all" />
    <type fullname="UI_SampleColor" preserve="all" />
    <type fullname="ADV.MainScenario" preserve="all" />
    <type fullname="RG.UI.WindowAnimation" preserve="all" />
    <type fullname="RG.Help.HelpElement" preserve="all" />
    <type fullname="ADV.ThumbnailCapture" preserve="all" />
    <type fullname="RG.Help.HelpWindow" preserve="all" />
    <type fullname="UI_RaycastCtrl" preserve="all" />
    <type fullname="RG.UI.DialogText" preserve="all" />
    <type fullname="ExitDialog" preserve="all" />
    <type fullname="RG.Config.ConfigGameSetting" preserve="all" />
    <type fullname="SlotMachineSetting" preserve="all" />
    <type fullname="CircleOutline" preserve="all" />
    <type fullname="EMTransition" preserve="all" />
    <type fullname="RG.UI.AutoRotater" preserve="all" />
    <type fullname="TypefaceAnimatorEx" preserve="all" />
    <type fullname="RG.Config.ConfigVoiceSetData" preserve="all" />
    <type fullname="RG.UI.ColorTint" preserve="all" />
    <type fullname="RG.Config.ConfigToggleElement" preserve="all" />
    <type fullname="Manager.FadePlayer" preserve="all" />
    <type fullname="GraphicDropDown" preserve="all" />
    <type fullname="RG.Config.EnterExitTextComponent" preserve="all" />
    <type fullname="MK.Glow.HDRP.MKGlow" preserve="all" />
    <type fullname="RG.UI.DialogImage" preserve="all" />
    <type fullname="ConfirmDialog" preserve="all" />
    <type fullname="ADV.ADVScene" preserve="all" />
    <type fullname="RG.Config.GraphicSetting" preserve="all" />
    <type fullname="SceneAssist.PointerEnterExitAction" preserve="all" />
    <type fullname="RG.Config.ConfigShortcutButton" preserve="all" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings" preserve="all" />
    <type fullname="Manager.Voice" preserve="all" />
    <type fullname="RG.Config.ConfigWindow" preserve="all" />
    <type fullname="CameraControl_Ver2" preserve="all" />
    <type fullname="RG.Config.CameraSetting" preserve="all" />
    <type fullname="ADV.CommandController" preserve="all" />
    <type fullname="RG.Settings.SceneSettings" preserve="all" />
    <type fullname="RG.Scene.Home.UI.BaseHomeIconUI" preserve="all" />
    <type fullname="Manager.Character" preserve="all" />
    <type fullname="RG.Config.SoundSetting" preserve="all" />
    <type fullname="BeautifyHDRP.Beautify" preserve="all" />
    <type fullname="ADV.ADVButton" preserve="all" />
    <type fullname="TextColorChanger" preserve="all" />
    <type fullname="RG.Config.AppendSetting" preserve="all" />
    <type fullname="ADV.BackLogComponent" preserve="all" />
    <type fullname="CharaCustom.BackgroundCtrl" preserve="all" />
    <type fullname="ADV.BackLog" preserve="all" />
    <type fullname="ADV.CommandController/CharaCorrectHeightCamera" preserve="nothing" serialized="true" />
    <type fullname="ADV.CommandController/CharaCorrectHeightCamera/Pair" preserve="nothing" serialized="true" />
    <type fullname="ADV.Info" preserve="nothing" serialized="true" />
    <type fullname="ADV.Info/Anime" preserve="nothing" serialized="true" />
    <type fullname="ADV.Info/Anime/Play" preserve="nothing" serialized="true" />
    <type fullname="ADV.Info/Audio" preserve="nothing" serialized="true" />
    <type fullname="ADV.Info/Audio/Eco" preserve="nothing" serialized="true" />
    <type fullname="ADV.MainScenario/ModeReactiveProperty" preserve="nothing" serialized="true" />
    <type fullname="ADV.OpenData" preserve="nothing" serialized="true" />
    <type fullname="ADV.Regulate" preserve="nothing" serialized="true" />
    <type fullname="BaseCameraControl_Ver2/CameraData" preserve="nothing" serialized="true" />
    <type fullname="BeautifyHDRP.Beautify/BeautifyBlinkStyleParameter" preserve="nothing" serialized="true" />
    <type fullname="BeautifyHDRP.Beautify/BeautifyDoFBokehCompositionParameter" preserve="nothing" serialized="true" />
    <type fullname="BeautifyHDRP.Beautify/BeautifyDoFFocusModeParameter" preserve="nothing" serialized="true" />
    <type fullname="BeautifyHDRP.Beautify/BeautifyLayerMaskParameter" preserve="nothing" serialized="true" />
    <type fullname="BeautifyHDRP.Beautify/BeautifyTonemapOperatorParameter" preserve="nothing" serialized="true" />
    <type fullname="CharaCustom.UI_Dropdown/DropdownEvent" preserve="nothing" serialized="true" />
    <type fullname="CharaCustom.UI_Dropdown/OptionData" preserve="nothing" serialized="true" />
    <type fullname="CharaCustom.UI_Dropdown/OptionDataList" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.HDRP.MKGlow/AntiFlickerModeParameter" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.HDRP.MKGlow/DebugViewParameter" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.HDRP.MKGlow/GlareStyleParameter" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.HDRP.MKGlow/LayerMaskParameter" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.HDRP.MKGlow/LensFlareStyleParameter" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.HDRP.MKGlow/MinMaxRangeParameter" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.HDRP.MKGlow/QualityParameter" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.HDRP.MKGlow/RenderPriorityParameter" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.HDRP.MKGlow/Texture2DParameter" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.HDRP.MKGlow/WorkflowParameter" preserve="nothing" serialized="true" />
    <type fullname="MK.Glow.MinMaxRange" preserve="nothing" serialized="true" />
    <type fullname="RG.Config.ConfigWindow/ShortCutGroup" preserve="nothing" serialized="true" />
    <type fullname="RG.Config.SoundSetting/SoundGroup" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.RangeValue`1[System.Int32]" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings/ABInfo" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings/CondEventPairDictionary" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings/EventDictionary" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings/IntArray" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings/IntRateArray" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings/MapIDs" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings/NPCEventDictionary" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings/ShopItemIDs" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings/TalkEventDictionary" preserve="nothing" serialized="true" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings/TalkEventDictionary2" preserve="nothing" serialized="true" />
    <type fullname="RG.Scripts.Velocities" preserve="nothing" serialized="true" />
    <type fullname="RG.Settings.SceneSettings/SceneDictionary" preserve="nothing" serialized="true" />
    <type fullname="RG.Settings.SceneSettings/SceneParameter" preserve="nothing" serialized="true" />
    <type fullname="SlotMachineSetting/RollAnim" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="IL, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Illusion.Unity.Component.GameScreenShot" preserve="all" />
    <type fullname="Illusion.Unity.UI.ColorPicker.Info" preserve="all" />
    <type fullname="Manager.Scene" preserve="all" />
    <type fullname="Illusion.Unity.UI.MouseButtonCheck" preserve="all" />
    <type fullname="Illusion.HDRP.CrossFadeControl" preserve="all" />
    <type fullname="Illusion.HDRP.PostProcessing.ILPostProcessResources" preserve="all" />
    <type fullname="FadeCanvas" preserve="all" />
    <type fullname="AssetBundleManager" preserve="all" />
    <type fullname="Illusion.HDRP.PostProcessing.CrossFade" preserve="all" />
    <type fullname="Illusion.Unity.UI.ColorPicker.PickerRectA" preserve="all" />
    <type fullname="SceneFadeCanvas" preserve="all" />
    <type fullname="Illusion.Unity.UI.ColorPicker.PickerRect/ModeReactiveProperty" preserve="nothing" serialized="true" />
    <type fullname="Illusion.Unity.UI.MouseButtonCheck/Callback" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Cinemachine, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Cinemachine.CinemachineVirtualCamera" preserve="all" />
    <type fullname="Cinemachine.CinemachineComposer" preserve="all" />
    <type fullname="Cinemachine.CinemachinePipeline" preserve="all" />
    <type fullname="Cinemachine.CinemachineBrain" preserve="all" />
    <type fullname="Cinemachine.CinemachineTransposer" preserve="all" />
    <type fullname="Cinemachine.CinemachineBlendDefinition" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBrain/BrainEvent" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineBrain/VcamActivatedEvent" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.CinemachineVirtualCameraBase/TransitionParams" preserve="nothing" serialized="true" />
    <type fullname="Cinemachine.LensSettings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Core.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.VolumeProfile" preserve="all" />
    <type fullname="UnityEngine.Rendering.Volume" preserve="all" />
    <type fullname="UnityEngine.Rendering.AnimationCurveParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.BitArray128" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.BoolParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.ClampedFloatParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.ClampedIntParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.ColorParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.CubemapParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.FloatParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.FloatRangeParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.IntParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.LayerMaskParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.MinFloatParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.NoInterpClampedIntParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.NoInterpIntParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.NoInterpMinFloatParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.NoInterpTextureParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.NoInterpVector2Parameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.TextureCurve" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.TextureCurveParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.TextureParameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.Vector2Parameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.Vector3Parameter" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.Vector4Parameter" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="RaindropFX.HDRP, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="RaindropFX.RDFXResources" preserve="all" />
    <type fullname="RaindropFX.RDFXResources/GenerateShaders" preserve="nothing" serialized="true" />
    <type fullname="RaindropFX.RDFXResources/VolumeShaders" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Obi, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Obi.ObiCollider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
  </assembly>
  <assembly fullname="UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEditor.Audio.AudioMixerSnapshotController" preserve="all" />
  </assembly>
  <assembly fullname="UniRx">
    <type fullname="UniRx.BoolReactiveProperty" preserve="nothing" serialized="true" />
    <type fullname="UniRx.IntReactiveProperty" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
  </assembly>
</linker>