{"m_buildTarget": "StandaloneWindows64", "m_SettingsHash": "", "m_CatalogLocations": [{"m_Keys": ["AddressablesMainContentCatalog"], "m_InternalId": "{UnityEngine.AddressableAssets.Addressables.RuntimePath}/catalog.json", "m_Provider": "UnityEngine.AddressableAssets.ResourceProviders.ContentCatalogProvider", "m_Dependencies": [], "m_ResourceType": {"m_AssemblyName": "Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_ClassName": "UnityEngine.AddressableAssets.ResourceLocators.ContentCatalogData"}, "SerializedData": []}], "m_ProfileEvents": false, "m_LogResourceManagerExceptions": true, "m_ExtraInitializationData": [], "m_DisableCatalogUpdateOnStart": false, "m_IsLocalCatalogInBundle": false, "m_CertificateHandlerType": {"m_AssemblyName": "", "m_ClassName": ""}, "m_AddressablesVersion": "1.18.19", "m_maxConcurrentWebRequests": 500, "m_CatalogRequestsTimeout": 0}