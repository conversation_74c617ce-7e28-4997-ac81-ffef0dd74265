{"names": ["UnityEngine.dll", "UnityEngine.AIModule.dll", "UnityEngine.ARModule.dll", "UnityEngine.AccessibilityModule.dll", "UnityEngine.AndroidJNIModule.dll", "UnityEngine.AnimationModule.dll", "UnityEngine.AssetBundleModule.dll", "UnityEngine.AudioModule.dll", "UnityEngine.ClothModule.dll", "UnityEngine.ClusterInputModule.dll", "UnityEngine.ClusterRendererModule.dll", "UnityEngine.CoreModule.dll", "UnityEngine.CrashReportingModule.dll", "UnityEngine.DSPGraphModule.dll", "UnityEngine.DirectorModule.dll", "UnityEngine.GIModule.dll", "UnityEngine.GameCenterModule.dll", "UnityEngine.GridModule.dll", "UnityEngine.HotReloadModule.dll", "UnityEngine.IMGUIModule.dll", "UnityEngine.ImageConversionModule.dll", "UnityEngine.InputModule.dll", "UnityEngine.InputLegacyModule.dll", "UnityEngine.JSONSerializeModule.dll", "UnityEngine.LocalizationModule.dll", "UnityEngine.ParticleSystemModule.dll", "UnityEngine.PerformanceReportingModule.dll", "UnityEngine.PhysicsModule.dll", "UnityEngine.Physics2DModule.dll", "UnityEngine.ProfilerModule.dll", "UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll", "UnityEngine.ScreenCaptureModule.dll", "UnityEngine.SharedInternalsModule.dll", "UnityEngine.SpriteMaskModule.dll", "UnityEngine.SpriteShapeModule.dll", "UnityEngine.StreamingModule.dll", "UnityEngine.SubstanceModule.dll", "UnityEngine.SubsystemsModule.dll", "UnityEngine.TLSModule.dll", "UnityEngine.TerrainModule.dll", "UnityEngine.TerrainPhysicsModule.dll", "UnityEngine.TextCoreModule.dll", "UnityEngine.TextRenderingModule.dll", "UnityEngine.TilemapModule.dll", "UnityEngine.UIModule.dll", "UnityEngine.UIElementsModule.dll", "UnityEngine.UIElementsNativeModule.dll", "UnityEngine.UNETModule.dll", "UnityEngine.UmbraModule.dll", "UnityEngine.UnityAnalyticsModule.dll", "UnityEngine.UnityConnectModule.dll", "UnityEngine.UnityCurlModule.dll", "UnityEngine.UnityTestProtocolModule.dll", "UnityEngine.UnityWebRequestModule.dll", "UnityEngine.UnityWebRequestAssetBundleModule.dll", "UnityEngine.UnityWebRequestAudioModule.dll", "UnityEngine.UnityWebRequestTextureModule.dll", "UnityEngine.UnityWebRequestWWWModule.dll", "UnityEngine.VFXModule.dll", "UnityEngine.VRModule.dll", "UnityEngine.VehiclesModule.dll", "UnityEngine.VideoModule.dll", "UnityEngine.VirtualTexturingModule.dll", "UnityEngine.WindModule.dll", "UnityEngine.XRModule.dll", "Assembly-CSharp-firstpass.dll", "Assembly-CSharp.dll", "Expanse.dll", "Obi.dll", "Unity.Addressables.dll", "Unity.ProBuilder.dll", "Unity.RenderPipelines.Core.ShaderLibrary.dll", "UnityEngine.UI.dll", "Unity.ProBuilder.Poly2Tri.dll", "ZString.dll", "UniTask.TextMeshPro.dll", "Cinemachine.dll", "MessagePack.dll", "KWS.dll", "Unity.RenderPipelines.HighDefinition.Config.Runtime.dll", "Unity.Collections.dll", "SimpleAnimationComponent.dll", "MessagePack.Annotations.dll", "RaindropFX.HDRP.dll", "UniTask.Addressables.dll", "Unity.VisualEffectGraph.Runtime.dll", "Unity.Timeline.dll", "Unity.TextMeshPro.dll", "Unity.RenderPipelines.HighDefinition.Runtime.dll", "Unity.ProBuilder.Csg.dll", "Unity.RenderPipelines.Core.Runtime.dll", "Unity.ResourceManager.dll", "UniTask.DOTween.dll", "Coffee.UnmaskForUGUI.dll", "UniTask.Linq.dll", "Unity.Mathematics.dll", "UniRx.dll", "Unity.ProBuilder.KdTree.dll", "Coffee.SoftMaskForUGUI.dll", "Unity.Burst.dll", "UIEffect.dll", "Unity.ProBuilder.Stl.dll", "Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll", "IL.dll", "Unity.ScriptableBuildPipeline.dll", "UniTask.dll", "Unity.Collections.LowLevel.ILSupport.dll", "System.Memory.dll", "System.Threading.Tasks.Extensions.dll", "I18N.MidEast.dll", "I18N.dll", "System.Buffers.dll", "Unity.Burst.Cecil.Pdb.dll", "FastEnum.dll", "System.Runtime.CompilerServices.Unsafe.dll", "I18N.Other.dll", "System.Windows.Forms.dll", "I18N.CJK.dll", "I18N.West.dll", "Unity.Burst.Unsafe.dll", "Unity.Burst.Cecil.Mdb.dll", "Ionic.Zip.dll", "Unity.Burst.Cecil.dll", "Unity.Burst.Cecil.Rocks.dll", "I18N.Rare.dll"], "types": [2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 2, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16, 16]}