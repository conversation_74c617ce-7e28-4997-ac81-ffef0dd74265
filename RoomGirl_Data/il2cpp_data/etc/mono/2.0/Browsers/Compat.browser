<!--
    This file defines some of the browsers that Microsoft's implementation provides in
    <windir>\Microsoft.NET\Framework\<ver>\CONFIG\Browsers\*.browser
	
	It is not derived from any file distributed with Microsoft's implementation.  Since
	we can't distribute <PERSON>'s browser files, we use browscap.ini to determine 
	browser capabilities.  Then, if and only if the application contains App_Browser/*.browser
	files and we are using .NET 2.0 or higher, we supplement the capabilities with the 
	information in those files and the files in this directory.  The primary goal of this file
	is provide browser definitions that might be referenced in App_Browser/*.browser files.
-->
<browsers>
  <defaultBrowser id="Default">
  </defaultBrowser>
  <browser id="Default">
    <identification>
    	<userAgent match="." />
    </identification>
  </browser>
  <browser id="IE6to9" parentID="Default">
    <identification>
      <capability name="majorver" match="^[6-9]" />
      <capability name="browser" match="^(IE|AOL)$" />
    </identification>
  </browser>
  <browser id="Opera8to9" parentID="Default">
    <identification>
      <capability name="majorver" match="^[8-9]" />
      <capability name="browser" match="^Opera$" />
    </identification>
  </browser>
  <browser id="Safari" parentID="Default">
    <identification>
      <capability name="browser" match="^Safari$" />
    </identification>
  </browser>
  <browser id="Mozilla" parentID="Default">
    <identification>
      <capability name="browser" match="^Mozilla" />
    </identification>
  </browser>
</browsers>