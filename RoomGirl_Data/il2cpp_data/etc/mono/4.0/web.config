<?xml version="1.0" encoding="utf-8"?>

<configuration>

  <system.codedom>
        <compilers>
            <compiler language="c#;cs;csharp" extension=".cs" warningLevel="4" type="Microsoft.CSharp.CSharpCodeProvider, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                <providerOption name="CompilerVersion" value="v4.0"/>
                <providerOption name="WarnAsError" value="false"/>
            </compiler>
            <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" warningLevel="4" type="Microsoft.VisualBasic.VBCodeProvider, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
                <providerOption name="CompilerVersion" value="v4.0"/>
                <providerOption name="OptionInfer" value="true"/>
                <providerOption name="WarnAsError" value="false"/>
            </compiler>
        </compilers>
  </system.codedom>

	<system.web>
		<monoSettings>
			<compilersCompatibility>
				<compiler language="c#;cs;csharp" extension=".cs" compilerOptions="/nowarn:0169"
					  type="Microsoft.CSharp.CSharpCodeProvider, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			</compilersCompatibility>
		</monoSettings>
		
		<authorization>
			<allow users="*" />
		</authorization>
		<httpHandlers>
		  <add path="trace.axd" verb="*" type="System.Web.Handlers.TraceHandler" validate="True" />
		  <add path="WebResource.axd" verb="GET" type="System.Web.Handlers.AssemblyResourceLoader" validate="True" />
		  <add verb="*" path="*_AppService.axd" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="False" />
		  <add verb="GET,HEAD" path="ScriptResource.axd" type="System.Web.Handlers.ScriptResourceHandler, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="False"/>
		  <add path="*.axd" verb="*" type="System.Web.HttpNotFoundHandler" validate="True" />
		  <add path="*.aspx" verb="*" type="System.Web.UI.PageHandlerFactory" validate="True" />
		  <add path="*.ashx" verb="*" type="System.Web.UI.SimpleHandlerFactory" validate="True" />
		  <add path="*.asmx" verb="*" type="System.Web.Script.Services.ScriptHandlerFactory, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="False" />
		  <add path="*.rem" verb="*" type="System.Runtime.Remoting.Channels.Http.HttpRemotingHandlerFactory, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" validate="False" />
		  <add path="*.soap" verb="*" type="System.Runtime.Remoting.Channels.Http.HttpRemotingHandlerFactory, System.Runtime.Remoting, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" validate="False" />
		  <add path="*.asax" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.ascx" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.master" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.skin" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.browser" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.sitemap" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.dll.config" verb="GET,HEAD" type="System.Web.StaticFileHandler" validate="True" />
		  <add path="*.exe.config" verb="GET,HEAD" type="System.Web.StaticFileHandler" validate="True" />
		  <add path="*.config" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.cs" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.csproj" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.vb" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.vbproj" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.webinfo" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.licx" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.resx" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.resources" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.mdb" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.vjsproj" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.java" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.jsl" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.ldb" verb="*" type="System.Web.HttpForbiddenHandler"  validate="True" />
		  <add path="*.ad" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.dd" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.ldd" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.sd" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.cd" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.adprototype" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.lddprototype" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.sdm" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.sdmDocument" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.mdf" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.ldf" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.exclude" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <add path="*.refresh" verb="*" type="System.Web.HttpForbiddenHandler" validate="True" />
		  <!--
		  <add path="*.svc" verb="*" type="System.ServiceModel.Activation.HttpHandler, System.ServiceModel.Activation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="False"/>
		  -->
                  <add verb="*" path="*.svc" type="System.ServiceModel.Channels.SvcHttpHandlerFactory, System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
		  <add path="*.rules" verb="*" type="System.Web.HttpForbiddenHandler" validate="True"/>
		  <!--
		  <add path="*.xoml" verb="*" type="System.ServiceModel.Activation.HttpHandler, System.ServiceModel.Activation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="False"/>
		  <add path="*.xamlx" verb="*" type="System.Xaml.Hosting.XamlHttpHandlerFactory, System.Xaml.Hosting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" validate="False"/>
		  -->
		  <add path="*" verb="GET,HEAD,POST" type="System.Web.DefaultHttpHandler" validate="True" />
		  <add path="*" verb="*" type="System.Web.HttpMethodNotAllowedHandler" validate="True" />
		</httpHandlers>
		<httpModules>
		  <add name="OutputCache" type="System.Web.Caching.OutputCacheModule" />
		  <add name="Session" type="System.Web.SessionState.SessionStateModule" />
		  <!--
		  <add name="WindowsAuthentication" type="System.Web.Security.WindowsAuthenticationModule" />
		  -->
		  <add name="FormsAuthentication" type="System.Web.Security.FormsAuthenticationModule" />
		  <!--
		  <add name="PassportAuthentication" type="System.Web.Security.PassportAuthenticationModule" />
		  -->
		  <add name="RoleManager" type="System.Web.Security.RoleManagerModule" />
		  <add name="UrlAuthorization" type="System.Web.Security.UrlAuthorizationModule" />
		  <!--
		  <add name="FileAuthorization" type="System.Web.Security.FileAuthorizationModule" />
		  -->
		  <add name="AnonymousIdentification" type="System.Web.Security.AnonymousIdentificationModule" />
		  <add name="Profile" type="System.Web.Profile.ProfileModule" />
		  <!--
		  <add name="ErrorHandlerModule" type="System.Web.Mobile.ErrorHandlerModule, System.Web.Mobile, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
		  <add name="ServiceModel" type="System.ServiceModel.Activation.HttpModule, System.ServiceModel.Activation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
		  -->
		  <add name="UrlRoutingModule-4.0" type="System.Web.Routing.UrlRoutingModule" />
		  <add name="ScriptModule-4.0" type="System.Web.Handlers.ScriptModule, System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
		</httpModules>
		<authentication mode="Forms">
			<forms name=".MONOAUTH" loginUrl="login.aspx" protection="All" timeout="30" path="/">
				<credentials passwordFormat="Clear">
					<!--<user name="gonzalo" password="gonz"/>-->
				</credentials>
			</forms>
		</authentication>
		<machineKey validationKey="AutoGenerate" decryptionKey="AutoGenerate" validation="SHA1" />
		<globalization  requestEncoding="utf-8"
				responseEncoding="utf-8"
				fileEncoding="utf-8"/>
		<!--
				culture="en-US"
				uiculture="en-US" />
		-->
		<sessionState mode="InProc" />
		<pages>
        		<namespaces>
            			<add namespace="System" />
            			<add namespace="System.Collections" />
            			<add namespace="System.Collections.Specialized" />
            			<add namespace="System.Configuration" />
            			<add namespace="System.Text" />
            			<add namespace="System.Text.RegularExpressions" />
            			<add namespace="System.Web" />
            			<add namespace="System.Web.Caching" />
            			<add namespace="System.Web.SessionState" />
            			<add namespace="System.Web.Security" />
            			<add namespace="System.Web.Profile" />
            			<add namespace="System.Web.UI" />
            			<add namespace="System.Web.UI.WebControls" />
            			<!-- <add namespace="System.Web.UI.WebControls.WebParts" /> -->
            			<add namespace="System.Web.UI.HtmlControls" />
        		</namespaces>
        		
        		<controls>
				<add tagPrefix="asp" namespace="System.Web.UI.WebControls.WebParts" assembly="System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<add tagPrefix="asp" namespace="System.Web.UI" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
				<add tagPrefix="asp" namespace="System.Web.UI.WebControls" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
				<add tagPrefix="asp" namespace="System.Web.UI.WebControls.Expressions" assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
				<add tagPrefix="asp" namespace="System.Web.DynamicData" assembly="System.Web.DynamicData, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
				<add tagPrefix="asp" namespace="System.Web.UI.WebControls" assembly="System.Web.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
			</controls>
    		</pages>
		<webControls clientScriptsLocation="/web_scripts" />
		<compilation debug="false" defaultLanguage="c#" explicit="true" strict="false" >
			<assemblies>
				<add assembly="mscorlib" />
				<add assembly="Microsoft.CSharp, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<add assembly="System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<add assembly="System.Configuration, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<add assembly="System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<add assembly="System.Data, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<add assembly="System.Web.Services, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<add assembly="System.Xml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<add assembly="System.Drawing, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<add assembly="System.EnterpriseServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<!-- <add assembly="System.Web.Mobile, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" /> -->
				<add assembly="System.IdentityModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<add assembly="System.Runtime.Serialization, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<add assembly="System.Xaml, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<add assembly="System.ServiceModel, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<!-- <add assembly="System.ServiceModel.Activation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/> -->
				<!-- <add assembly="System.ServiceModel.Channels, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/> -->
				<add assembly="System.ServiceModel.Web, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
				<!-- <add assembly="System.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/> -->
				<!-- <add assembly="System.ServiceModel.Activities, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/> -->
				<!-- <add assembly="System.WorkflowServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/> -->
				<!-- <add assembly="System.Xaml.Hosting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/> -->
				<add assembly="System.Core, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<add assembly="System.Web.Extensions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
				<add assembly="System.Data.DataSetExtensions, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<add assembly="System.Xml.Linq, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<add assembly="System.ComponentModel.DataAnnotations, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
				<add assembly="System.Web.DynamicData, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
				<!-- <add assembly="System.Data.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" /> -->
				<!-- <add assembly="System.Web.Entity, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089"/> -->
				<add assembly="System.Data.Linq, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" />
				<!-- <add assembly="System.Data.Entity.Design, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" /> -->
				<add assembly="System.Web.ApplicationServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
				<add assembly="*" /> <!-- Add assemblies in bin directory -->
			</assemblies>
			<expressionBuilders>
				<add expressionPrefix="Resources"
				     type="System.Web.Compilation.ResourceExpressionBuilder, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<add expressionPrefix="ConnectionStrings"
				     type="System.Web.Compilation.ConnectionStringsExpressionBuilder, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<add expressionPrefix="AppSettings"
				     type="System.Web.Compilation.AppSettingsExpressionBuilder, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a" />
				<add expressionPrefix="RouteUrl" type="System.Web.Compilation.RouteUrlExpressionBuilder"/>
				<!--
				<add expressionPrefix="RouteValue" type="System.Web.Compilation.RouteValueExpressionBuilder"/>
				-->
			</expressionBuilders>
			<buildProviders>
				<add extension=".aspx" type="System.Web.Compilation.PageBuildProvider" />
				<add extension=".ascx" type="System.Web.Compilation.UserControlBuildProvider" />
				<add extension=".master" type="System.Web.Compilation.MasterPageBuildProvider" />
				<add extension=".asmx" type="System.Web.Compilation.WebServiceBuildProvider" />
				<add extension=".ashx" type="System.Web.Compilation.WebHandlerBuildProvider" />
				<add extension=".soap" type="System.Web.Compilation.WebServiceBuildProvider" />
				<add extension=".resx" type="System.Web.Compilation.ResXBuildProvider" />
				<add extension=".resources" type="System.Web.Compilation.ResourcesBuildProvider" />
				<add extension=".wsdl" type="System.Web.Compilation.WsdlBuildProvider" />
				<add extension=".xsd" type="System.Web.Compilation.XsdBuildProvider" />
				<add extension=".js" type="System.Web.Compilation.ForceCopyBuildProvider" />
				<add extension=".lic" type="System.Web.Compilation.IgnoreFileBuildProvider" />
				<add extension=".licx" type="System.Web.Compilation.IgnoreFileBuildProvider" />
				<add extension=".exclude" type="System.Web.Compilation.IgnoreFileBuildProvider" />
				<add extension=".refresh" type="System.Web.Compilation.IgnoreFileBuildProvider" />
				<!--
				<add extension=".edmx" type="System.Data.Entity.Design.AspNet.EntityDesignerBuildProvider" />
				<add extension=".xoml" type="System.ServiceModel.Activation.WorkflowServiceBuildProvider, System.WorkflowServices, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35"/>
				<add extension=".svc" type="System.ServiceModel.Activation.ServiceBuildProvider, System.ServiceModel.Activation, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
				<add extension=".xamlx" type="System.Xaml.Hosting.XamlBuildProvider, System.Xaml.Hosting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" />
				-->
			</buildProviders>
		</compilation>
		<httpRuntime executionTimeout="110"
			     maxRequestLength="4096"
			     useFullyQualifiedRedirectUrl="false"
			     minFreeThreads="8"
			     minLocalRequestFreeThreads="4"
			     appRequestQueueLimit="5000" />
		<clientTarget>
			<add alias="ie5" userAgent="Mozilla/4.0 (compatible; MSIE 5.5; Windows NT 4.0)" />
			<add alias="ie4" userAgent="Mozilla/4.0 (compatible; MSIE 4.0; Windows NT 4.0)" />
			<add alias="uplevel" userAgent="Mozilla/4.0 (compatible; MSIE 4.0; Windows NT 4.0)" />
			<add alias="downlevel" userAgent="Unknown" />
		</clientTarget>

		<siteMap>
			<providers>
				<add name="AspNetXmlSiteMapProvider"
				 description="Default site map provider that reads in .sitemap xml files."
				 type="System.Web.XmlSiteMapProvider, System.Web, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a"
				 siteMapFile="Web.sitemap" />
			</providers>
		</siteMap>
	</system.web>

</configuration>
