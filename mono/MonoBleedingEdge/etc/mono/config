<configuration>
	<dllmap dll="i:cygwin1.dll" target="libc.so.6" os="!windows" />
	<dllmap dll="libc" target="libc.so.6" os="!windows"/>
	<dllmap dll="intl" target="libc.so.6" os="!windows"/>
	<dllmap dll="intl" name="bind_textdomain_codeset" target="libc.so.6" os="solaris"/>
	<dllmap dll="libintl" name="bind_textdomain_codeset" target="libc.so.6" os="solaris"/>
	<dllmap dll="libintl" target="libc.so.6" os="!windows"/>
	<dllmap dll="i:libxslt.dll" target="libxslt.so" os="!windows"/>
	<dllmap dll="i:odbc32.dll" target="libodbc.so.2" os="!windows"/>
	<dllmap dll="i:odbc32.dll" target="libiodbc.dylib" os="osx"/>
	<dllmap dll="oci" target="libclntsh.so" os="!windows"/>
	<dllmap dll="db2cli" target="libdb2_36.so" os="!windows"/>
	<dllmap dll="MonoPosixHelper" target="$mono_libdir/../MonoBleedingEdge/EmbedRuntime/libMonoPosixHelper.so" os="!windows" />
	<dllmap dll="MonoPosixHelper" target="$mono_libdir/../MonoBleedingEdge/EmbedRuntime/MonoPosixHelper.dll" os="windows" />
	<dllmap dll="System.Native" target="$mono_libdir/libmono-native.so" os="!windows" />
	<dllmap dll="System.Net.Security.Native" target="$mono_libdir/../MonoBleedingEdge/EmbedRuntime/libmono-native.so" os="!windows" />
	<dllmap dll="System.Security.Cryptography.Native.Apple" target="$mono_libdir/libmono-native.so" os="osx" />
	<dllmap dll="libmono-btls-shared" target="$mono_libdir/../MonoBleedingEdge/EmbedRuntime/libmono-btls-shared.so" os="!windows" />
	<dllmap dll="libmono-btls-shared" target="$mono_libdir/../MonoBleedingEdge/EmbedRuntime/libmono-btls-shared.dll" os="windows" />
	<dllmap dll="i:msvcrt" target="libc.so.6" os="!windows"/>
	<dllmap dll="i:msvcrt.dll" target="libc.so.6" os="!windows"/>
	<dllmap dll="sqlite" target="libsqlite.so.0" os="!windows"/>
	<dllmap dll="sqlite3" target="libsqlite3.so.0" os="!windows"/>
	<dllmap dll="libX11" target="libX11.so" os="!windows" />
	<dllmap dll="libgdk-x11-2.0" target="libgdk-x11-2.0.so.0" os="!windows"/>
	<dllmap dll="libgdk_pixbuf-2.0" target="libgdk_pixbuf-2.0.so.0" os="!windows"/>
	<dllmap dll="libgtk-x11-2.0" target="libgtk-x11-2.0.so.0" os="!windows"/>
	<dllmap dll="libglib-2.0" target="libglib-2.0.so.0" os="!windows"/>
	<dllmap dll="libgobject-2.0" target="libgobject-2.0.so.0" os="!windows"/>
	<dllmap dll="libgnomeui-2" target="libgnomeui-2.so.0" os="!windows"/>
	<dllmap dll="librsvg-2" target="librsvg-2.so.2" os="!windows"/>
	<dllmap dll="libXinerama" target="libXinerama.so.1" os="!windows" />
	<dllmap dll="libasound" target="libasound.so.2" os="!windows" />
	<dllmap dll="libcairo-2.dll" target="libcairo.so.2" os="!windows"/>
	<dllmap dll="libcairo-2.dll" target="libcairo.2.dylib" os="osx"/>
	<dllmap dll="libcups" target="libcups.so.2" os="!windows"/>
	<dllmap dll="libcups" target="libcups.dylib" os="osx"/>
	<dllmap dll="i:kernel32.dll">
		<dllentry dll="__Internal" name="CopyMemory" target="mono_win32_compat_CopyMemory"/>
		<dllentry dll="__Internal" name="FillMemory" target="mono_win32_compat_FillMemory"/>
		<dllentry dll="__Internal" name="MoveMemory" target="mono_win32_compat_MoveMemory"/>
		<dllentry dll="__Internal" name="ZeroMemory" target="mono_win32_compat_ZeroMemory"/>
	</dllmap>
	<dllmap dll="gdiplus" target="libgdiplus.so" os="!windows"/>
	<dllmap dll="gdiplus.dll" target="libgdiplus.so"  os="!windows"/>
	<dllmap dll="gdi32" target="libgdiplus.so" os="!windows"/>
	<dllmap dll="gdi32.dll" target="libgdiplus.so" os="!windows"/>
</configuration>
