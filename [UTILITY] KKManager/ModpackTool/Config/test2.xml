<?xml version="1.0"?>
<ModpackToolConfiguration>
  <IngestFolder>E:\test\malformed</IngestFolder>
  <OutputFolder>E:\test\output</OutputFolder>
  <TestGameFolder>F:\Games\KoikatsuP</TestGameFolder>
  <FailFolder>E:\test\fail</FailFolder>
  <BackupFolder>E:\test\backup</BackupFolder>
  <Game1Short>KK</Game1Short>
  <Game1Longs>Koikatsu, Koikatu, コイカツ</Game1Longs>
  <Game2Short>KKS</Game2Short>
  <Game2Longs>Koikatsu Sunshine, Koika<PERSON>, コイカツ Sunshine</Game2Longs>
  <Game3Short>EC</Game3Short>
  <Game3Longs>EmotionCreators, Emotion Creators</Game3Longs>
  <GameOutputSubfolder>Sideloader Modpack - Exclusive</GameOutputSubfolder>
  <ContentPolicies>
    <ContentPolicy Kind="Unknown" OutputSubfolder="test" CanCompress="False" NeverPutInsideGameSpecific="False" />
    <ContentPolicy Kind="MapFreeH" OutputSubfolder="test1" CanCompress="False" NeverPutInsideGameSpecific="False" />
    <ContentPolicy Kind="MapStudio" OutputSubfolder="test2" CanCompress="False" NeverPutInsideGameSpecific="False" />
    <ContentPolicy Kind="AnimationFreeH" OutputSubfolder="test3" CanCompress="False" NeverPutInsideGameSpecific="False" />
    <ContentPolicy Kind="AnimationStudio" OutputSubfolder="test4" CanCompress="False" NeverPutInsideGameSpecific="False" />
    <ContentPolicy Kind="Studio" OutputSubfolder="test5" CanCompress="True" NeverPutInsideGameSpecific="True" />
    <ContentPolicy Kind="Character" OutputSubfolder="test6" CanCompress="False" NeverPutInsideGameSpecific="False" />
    <ContentPolicy Kind="UncensorSelector" OutputSubfolder="test7" CanCompress="True" NeverPutInsideGameSpecific="True" />
    <ContentPolicy Kind="MaterialEditor" OutputSubfolder="test8" CanCompress="True" NeverPutInsideGameSpecific="True" />
  </ContentPolicies>
</ModpackToolConfiguration>