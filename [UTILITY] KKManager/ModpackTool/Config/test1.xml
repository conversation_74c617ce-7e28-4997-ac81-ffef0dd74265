<?xml version="1.0"?>
<ModpackToolConfiguration>
  <IngestFolder>E:\test\_input</IngestFolder>
  <OutputFolder>E:\test\output</OutputFolder>
  <TestGameFolder>F:\Games\KoikatsuP</TestGameFolder>
  <FailFolder>E:\test\fail</FailFolder>
  <BackupFolder>E:\test\backup</BackupFolder>
  <LooseFilesFolder>E:\test\cards</LooseFilesFolder>
  <Game1Short>KK</Game1Short>
  <Game1Longs>Koikatsu, Koikatu, コイカツ</Game1Longs>
  <Game2Short>KKS</Game2Short>
  <Game2Longs>Koikatsu Sunshine, Koikatu Sunshine, コイカツ Sunshine</Game2Longs>
  <Game3Short>EC</Game3Short>
  <Game3Longs>EmotionCreators, Emotion Creators</Game3Longs>
  <GameOutputSubfolder>Sideloader Modpack - Exclusive</GameOutputSubfolder>
  <CompressPNGs>True</CompressPNGs>
  <RandomizeCABs>True</RandomizeCABs>
  <ContentPolicies>
    <ContentPolicy Kind="Unknown" OutputSubfolder="Sideloader Modpack" CanCompress="True" NeverPutInsideGameSpecific="False" />
    <ContentPolicy Kind="MapFreeH" OutputSubfolder="Sideloader Modpack - Maps" CanCompress="True" NeverPutInsideGameSpecific="True" />
    <ContentPolicy Kind="MapStudio" OutputSubfolder="Sideloader Modpack - Maps" CanCompress="True" NeverPutInsideGameSpecific="True" />
    <ContentPolicy Kind="AnimationFreeH" OutputSubfolder="Sideloader Modpack - Animations" CanCompress="True" NeverPutInsideGameSpecific="True" />
    <ContentPolicy Kind="AnimationStudio" OutputSubfolder="Sideloader Modpack - Animations" CanCompress="True" NeverPutInsideGameSpecific="True" />
    <ContentPolicy Kind="Studio" OutputSubfolder="Sideloader Modpack - Studio" CanCompress="True" NeverPutInsideGameSpecific="True" />
    <ContentPolicy Kind="Character" OutputSubfolder="Sideloader Modpack" CanCompress="True" NeverPutInsideGameSpecific="False" />
    <ContentPolicy Kind="UncensorSelector" OutputSubfolder="Sideloader Modpack - KK_UncensorSelector" CanCompress="True" NeverPutInsideGameSpecific="True" />
    <ContentPolicy Kind="MaterialEditor" OutputSubfolder="Sideloader Modpack - KK_MaterialEditor" CanCompress="True" NeverPutInsideGameSpecific="True" />
  </ContentPolicies>
</ModpackToolConfiguration>