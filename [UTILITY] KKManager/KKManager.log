﻿--------------------------------------------------
[15:37:29] Application startup - KKManager v1.0.0.0 Amd64
[15:37:29] Following language files are present but they are not specified as supported in LanguageManager: de, fr
[15:37:32] Game: Room Girl   Path: X:\RoomGirl Paradise
[15:37:32] Start loading zipmods from [X:\RoomGirl Paradise\mods]
[15:37:32] Start loading plugins from [X:\RoomGirl Paradise\BepInEx]
[15:37:32] Finished loading plugins from [X:\RoomGirl Paradise\BepInEx] in 116ms
[15:37:32] Finished loading zipmods from [X:\RoomGirl Paradise\mods] in 162ms
[15:37:32] Found 1 extended data deserializers
[15:37:32] System.ArgumentOutOfRangeException: Non-negative number required.
Parameter name: value
   at void System.IO.FileStream.set_Position(long value)
   at Image KKManager.Data.Cards.Card.GetCardFaceImage()
   at void KKManager.Windows.Content.CardWindow.RefreshThumbnails(bool additive, CharacterRange refreshRange)+CardThumbLoader()
[15:37:32] System.ArgumentOutOfRangeException: Non-negative number required.
Parameter name: value
   at void System.IO.FileStream.set_Position(long value)
   at Image KKManager.Data.Cards.Card.GetCardFaceImage()
   at void KKManager.Windows.Content.CardWindow.RefreshThumbnails(bool additive, CharacterRange refreshRange)+CardThumbLoader()
--------------------------------------------------
[15:52:02] Application startup - KKManager v1.0.0.0 Amd64
[15:52:03] Following language files are present but they are not specified as supported in LanguageManager: de, fr
[15:52:05] Game: Room Girl   Path: X:\RoomGirl Paradise
[15:52:05] Start loading zipmods from [X:\RoomGirl Paradise\mods]
[15:52:05] Start loading plugins from [X:\RoomGirl Paradise\BepInEx]
[15:52:05] Finished loading plugins from [X:\RoomGirl Paradise\BepInEx] in 103ms
[15:52:05] Finished loading zipmods from [X:\RoomGirl Paradise\mods] in 133ms
[15:52:05] Found 1 extended data deserializers
[15:52:05] System.ArgumentOutOfRangeException: Non-negative number required.
Parameter name: value
   at void System.IO.FileStream.set_Position(long value)
   at Image KKManager.Data.Cards.Card.GetCardFaceImage()
   at void KKManager.Windows.Content.CardWindow.RefreshThumbnails(bool additive, CharacterRange refreshRange)+CardThumbLoader()
[15:52:05] System.ArgumentOutOfRangeException: Non-negative number required.
Parameter name: value
   at void System.IO.FileStream.set_Position(long value)
   at Image KKManager.Data.Cards.Card.GetCardFaceImage()
   at void KKManager.Windows.Content.CardWindow.RefreshThumbnails(bool additive, CharacterRange refreshRange)+CardThumbLoader()
