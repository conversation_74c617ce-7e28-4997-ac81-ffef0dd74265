<?xml version="1.0"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="SB3Utility.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    </sectionGroup>
  </configSections>
  <startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.5"/></startup>
	<runtime>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<probing privatePath="plugins"/>
		</assemblyBinding>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="System.ValueTuple" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
			</dependentAssembly>
		</assemblyBinding>
		<assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
			<dependentAssembly>
				<assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
				<bindingRedirect oldVersion="0.0.0.0-4.2.0.0" newVersion="4.2.0.0" />
			</dependentAssembly>
		</assemblyBinding>
		<loadFromRemoteSources enabled="true"/>
	</runtime>
 <applicationSettings>
  <SB3Utility.Properties.Settings>
   <setting name="PluginsDoNotLoad" serializeAs="String">
    <value>libfbxsdk.dll;SB3UtilityFBX.dll;SB3UtilityPP.dll;niflib_x64.dll;SB3UtilityNIF.dll</value>
   </setting>
   <setting name="Wireframe" serializeAs="String">
    <value>False</value>
   </setting>
   <setting name="ShowNormals" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="ShowBones" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="Culling" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="AutoCenterView" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="Sensitivity" serializeAs="String">
    <value>0.01</value>
   </setting>
   <setting name="KeepBackupOfPP" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="KeepBackupOfXX" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="CaptureCommands" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="AutosaveCommands" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="LightDiffuseARGB" serializeAs="String">
    <value>FFC0C0C0</value>
   </setting>
   <setting name="LightAmbientARGB" serializeAs="String">
    <value>FF4C4C4C</value>
   </setting>
   <setting name="LightSpecularARGB" serializeAs="String">
    <value>FF606060</value>
   </setting>
   <setting name="FbxExportAnimationEulerFilter" serializeAs="String">
    <value>False</value>
   </setting>
   <setting name="FbxExportAnimationFilterPrecision" serializeAs="String">
    <value>0.25</value>
   </setting>
   <setting name="QuickAccess" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="ShowBoneWeights" serializeAs="String">
    <value>Strong</value>
   </setting>
   <setting name="KeepBackupOfXA" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="LeftTop" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="WidthHeight" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="BackupExtensionPP" serializeAs="String">
    <value>.pp</value>
   </setting>
   <setting name="WorkspaceLogMessages" serializeAs="String">
    <value>False</value>
   </setting>
   <setting name="WorkspaceScripting" serializeAs="String">
    <value>False</value>
   </setting>
   <setting name="MeshExportFormat" serializeAs="String">
    <value>FBX 2020.3.1</value>
   </setting>
   <setting name="WorkspaceAutomaticReopen" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="ExportMqoSortMeshes" serializeAs="String">
    <value>False</value>
   </setting>
   <setting name="PrivateMemSwapThresholdMB" serializeAs="String">
    <value>386</value>
   </setting>
   <setting name="TreeViewFontSize" serializeAs="String">
    <value>0</value>
   </setting>
   <setting name="ListViewFontSize" serializeAs="String">
    <value>0</value>
   </setting>
   <setting name="DialogXXDragDropSize" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="DialogXADragDropSize" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="DialogXXNormalsSize" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="DialogPPRenameSize" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="DialogPPRegisterToolSize" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="DialogXXConvertSize" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="DialogPPSubfileChangeSize" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="DialogXXSnapBordersSize" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="RendererBackgroundARGB" serializeAs="String">
    <value>FF0A0A3C</value>
   </setting>
   <setting name="FbxImportAnimationNegateQuaternionFlips" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="DialogPPMultiRenameSize" serializeAs="String">
    <value>0, 0</value>
   </setting>
   <setting name="FbxImportAnimationForceTypeSampled" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="MqoImportVertexScaling" serializeAs="String">
    <value>10</value>
   </setting>
   <setting name="CommandsTimestamp" serializeAs="String">
    <value>False</value>
   </setting>
   <setting name="LogEntriesTimestamp" serializeAs="String">
    <value>False</value>
   </setting>
   <setting name="Image" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="Renderer" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="Log" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="Script" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="Docking" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="FbxImportAverageNormals" serializeAs="String">
    <value>True</value>
   </setting>
   <setting name="normalsLength" serializeAs="String">
    <value>0.01</value>
   </setting>
   <setting name="tangentsLength" serializeAs="String">
    <value>0.005</value>
   </setting>
   <setting name="boneSize" serializeAs="String">
    <value>1</value>
   </setting>
   <setting name="PreviousSessionsMaxSessions" serializeAs="String">
    <value>10</value>
   </setting>
   <setting name="PreviousSessionsMaxFilesPerSession" serializeAs="String">
    <value>20</value>
   </setting>
   <setting name="FolderBrowserDialogRoot" serializeAs="String">
    <value>17</value>
   </setting>
  </SB3Utility.Properties.Settings>
 </applicationSettings>
</configuration>
