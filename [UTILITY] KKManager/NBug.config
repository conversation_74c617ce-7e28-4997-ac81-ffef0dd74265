﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    <sectionGroup name="applicationSettings" type="System.Configuration.ApplicationSettingsGroup, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089">
      <section name="NBug.Properties.Settings" type="System.Configuration.ClientSettingsSection, System, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
    </sectionGroup>
  </configSections>
  <connectionStrings></connectionStrings>
  <applicationSettings>
    <NBug.Properties.Settings>
      <setting name="UIMode" serializeAs="String">
        <value>Normal</value>
      </setting>
      <setting name="UIProvider" serializeAs="String">
        <value>WinForms</value>
      </setting>
      <setting name="SleepBeforeSend" serializeAs="String">
        <value>10</value>
      </setting>
      <setting name="MaxQueuedReports" serializeAs="String">
        <value>5</value>
      </setting>
      <setting name="StopReportingAfter" serializeAs="String">
        <value>30</value>
      </setting>
      <setting name="MiniDumpType" serializeAs="String">
        <value>Tiny</value>
      </setting>
      <setting name="WriteLogToDisk" serializeAs="String">
        <value>true</value>
      </setting>
      <setting name="ExitApplicationImmediately" serializeAs="String">
        <value>true</value>
      </setting>
      <setting name="HandleProcessCorruptedStateExceptions" serializeAs="String">
        <value>false</value>
      </setting>
      <setting name="ReleaseMode" serializeAs="String">
        <value>true</value>
      </setting>
      <setting name="DeferredReporting" serializeAs="String">
        <value>true</value>
      </setting>
      <setting name="StoragePath" serializeAs="String">
        <value>CurrentDirectory</value>
      </setting>
    </NBug.Properties.Settings>
  </applicationSettings>
</configuration>