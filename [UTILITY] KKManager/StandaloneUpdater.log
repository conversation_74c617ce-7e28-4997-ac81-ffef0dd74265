﻿--------------------------------------------------
[15:51:12] Application startup - StandaloneUpdater v1.0.0.0 Amd64
[15:51:12] Following language files are present but they are not specified as supported in LanguageManager: de, fr
[15:51:12] Arguments: X:\RoomGirl Paradise
[15:51:13] [SelfUpdater] A new KKManager version is available: 1.5
[15:51:25] Looking for update sources...
[15:51:26] Found 5 sources
[15:51:41] Starting update search...
[15:51:41] Start loading zipmods from [X:\RoomGirl Paradise\mods]
[15:51:41] Finished loading zipmods from [X:\RoomGirl Paradise\mods] in 96ms
[15:51:43] [sideload.betterrepack.com] Failed to download Updates file Updates.xml - Failed to download file
[15:51:44] [sideload.betterrepack.com] Failed to download Updates file Updates1.xml - Failed to download file
[15:51:44] [sideload.betterrepack.com] Failed to download Updates file Updates2.xml - Failed to download file
[15:51:46] [eus3.betterrepack.com] Failed to download Updates file Updates1.xml - Failed to download file
[15:51:46] [eus3.betterrepack.com] Failed to download Updates file Updates2.xml - Failed to download file
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack KK because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack - Bleeding Edge because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack - Fixes because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack - KK_MaterialEditor because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack - KK_UncensorSelector because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack - Maps because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack - Studio because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack - Animations because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping ECSideloader Modpack because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping ECSideloader Modpack UncensorSelector because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping EC Sideloader Modpack Fixes because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping AISideloader Modpack because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping AISideloader Modpack - MaterialEditor Shaders because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping AISideloader Modpack - Uncensor Selector because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping AISideloader Modpack - Maps because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping AISideloader Modpack - Studio because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping AISideloader Modpack - BleedingEdge because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping AISideloader Modpack - Exclusive because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping HS2Sideloader Modpack - Exclusive because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping KKUserData because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping HS2UserData because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping AISUserData because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping KKCCP because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping dhh.presets because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping AISideloader Modpack - Game Maps because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping KKSTrial Sideloader Modpack because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack - Exclusive KKS because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack - KKS_UncensorSelector because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping Sideloader Modpack - KKS_Maps because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping ECSideloader Modpack Old because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping ECSideloader Modpack UncensorSelector old because of conditions
[15:51:46] [eus3.betterrepack.com] Skipping EC Sideloader Modpack Fixes old because of conditions
[15:51:47] [eus2.betterrepack.com] Failed to download Updates file Updates2.xml - Failed to download file
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack KK because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack - Bleeding Edge because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack - Fixes because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack - KK_MaterialEditor because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack - KK_UncensorSelector because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack - Maps because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack - Studio because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack - Animations because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping ECSideloader Modpack because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping ECSideloader Modpack UncensorSelector because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping EC Sideloader Modpack Fixes because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping AISideloader Modpack because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping AISideloader Modpack - MaterialEditor Shaders because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping AISideloader Modpack - Uncensor Selector because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping AISideloader Modpack - Maps because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping AISideloader Modpack - Studio because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping AISideloader Modpack - BleedingEdge because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping AISideloader Modpack - Exclusive because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping HS2Sideloader Modpack - Exclusive because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping KKUserData because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping HS2UserData because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping AISUserData because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping KKCCP because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping dhh.presets because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping AISideloader Modpack - Game Maps because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping KKSTrial Sideloader Modpack because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack - Exclusive KKS because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack - KKS_UncensorSelector because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping Sideloader Modpack - KKS_Maps because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping ECSideloader Modpack Old because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping ECSideloader Modpack UncensorSelector old because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping EC Sideloader Modpack Fixes old because of conditions
[15:51:47] [eus2.betterrepack.com] Skipping kkrx10.h2 because of conditions
[15:51:47] [ERROR] Unexpected error while collecting updates from source sideload.betterrepack.com - skipping the source. Error: System.IO.FileNotFoundException: [sideload.betterrepack.com] Failed to get update list, check previous log for details.
   at async Task<List<UpdateTask>> KKManager.Updater.Sources.UpdateSourceBase.GetUpdateItems(CancellationToken cancellationToken, bool onlyDiscover, IProgress<float> progressCallback)
   at async Task<List<UpdateTask>> KKManager.Updater.Sources.FtpUpdater.GetUpdateItems(CancellationToken cancellationToken, bool onlyDiscover, IProgress<float> progressCallback)
   at async Task KKManager.Updater.UpdateSourceManager+<>c__DisplayClass1_1.<GetUpdates>g__DoUpdate|4(?)+DoUpdate(?)
   at async Task<List<UpdateTask>> KKManager.Updater.UpdateSourceManager.GetUpdates(CancellationToken cancellationToken, UpdateSourceBase[] updateSources, string[] filterByGuids, bool onlyDiscover, IProgress<float> progressCallback)
[15:51:48] [betterrepack.com] Failed to download Updates file Updates2.xml - Failed to download file
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - Exclusive KK and KKS because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack KK because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - Bleeding Edge because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - Fixes because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - KK_MaterialEditor because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - KK_UncensorSelector because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - Maps because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - Studio because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - Animations because of conditions
[15:51:48] [betterrepack.com] Skipping ECSideloader Modpack because of conditions
[15:51:48] [betterrepack.com] Skipping ECSideloader Modpack UncensorSelector because of conditions
[15:51:48] [betterrepack.com] Skipping EC Sideloader Modpack Fixes because of conditions
[15:51:48] [betterrepack.com] Skipping AISideloader Modpack because of conditions
[15:51:48] [betterrepack.com] Skipping AISideloader Modpack - MaterialEditor Shaders because of conditions
[15:51:48] [betterrepack.com] Skipping AISideloader Modpack - Uncensor Selector because of conditions
[15:51:48] [betterrepack.com] Skipping AISideloader Modpack - Maps because of conditions
[15:51:48] [betterrepack.com] Skipping AISideloader Modpack - Studio because of conditions
[15:51:48] [betterrepack.com] Skipping AISideloader Modpack - BleedingEdge because of conditions
[15:51:48] [betterrepack.com] Skipping AISideloader Modpack - Exclusive because of conditions
[15:51:48] [betterrepack.com] Skipping HS2Sideloader Modpack - Exclusive because of conditions
[15:51:48] [betterrepack.com] Skipping HS2UserData because of conditions
[15:51:48] [betterrepack.com] Skipping AISUserData because of conditions
[15:51:48] [betterrepack.com] Skipping KKCCP because of conditions
[15:51:48] [betterrepack.com] Skipping dhh.presets because of conditions
[15:51:48] [betterrepack.com] Skipping AISideloader Modpack - Game Maps because of conditions
[15:51:48] [betterrepack.com] Skipping KKSTrial Sideloader Modpack because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - Exclusive KKS because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - KKS_UncensorSelector because of conditions
[15:51:48] [betterrepack.com] Skipping Sideloader Modpack - KKS_Maps because of conditions
[15:51:48] [betterrepack.com] Skipping ECSideloader Modpack Old because of conditions
[15:51:48] [betterrepack.com] Skipping ECSideloader Modpack UncensorSelector old because of conditions
[15:51:48] [betterrepack.com] Skipping EC Sideloader Modpack Fixes old because of conditions
[15:51:48] [betterrepack.com] Skipping aisr9.1h1 because of conditions
[15:51:48] [betterrepack.com] Skipping kkrx10.h2 because of conditions
[15:51:48] [betterrepack.com] Skipping phr32.h1 because of conditions
[15:51:48] [betterrepack.com] Skipping hs2.401.h because of conditions
[15:51:48] [betterrepack.com] Skipping hs2.5.h because of conditions
[15:51:48] [betterrepack.com] Skipping hs2.5.h.c because of conditions
[15:51:48] [betterrepack.com] Skipping hs2.5.hdhh because of conditions
[15:51:48] [betterrepack.com] Skipping hs2.5.h.cdhh because of conditions
[15:51:48] [betterrepack.com] Skipping kk.101.h because of conditions
[15:51:48] [betterrepack.com] Skipping kkS.1.2.h1 because of conditions
[15:51:48] [betterrepack.com] Skipping HC Cockblocker because of conditions
[15:51:48] [betterrepack.com] Skipping kkrx10.h2 because of conditions
[15:51:48] [ERROR] Unexpected error while collecting updates from source hf.honeyselect2.com - skipping the source. Error: System.Net.Sockets.SocketException: Could not resolve host 'hf.honeyselect2.com'
   at void System.Net.Dns.Error_11001(string hostName)
   at IPHostEntry System.Net.Dns.GetHostByName(string hostName)
   at IPHostEntry System.Net.Dns.GetHostEntry(string hostNameOrAddress)
   at IPAddress[] System.Net.Dns.GetHostAddresses(string hostNameOrAddress)
   at IPAddress[] System.Net.Dns.EndGetHostAddresses(IAsyncResult asyncResult)
   at void System.Threading.Tasks.TaskFactory<System.Net.IPAddress[]>.FromAsyncCoreLogic(IAsyncResult iar, Func<IAsyncResult, IPAddress[]> endFunction, Action<IAsyncResult> endAction, Task<IPAddress[]> promise, bool requiresSynchronization)
   at async Task FluentFTP.FtpSocketStream.ConnectAsync(string host, int port, FtpIpVersion ipVersions, CancellationToken token)
   at async Task FluentFTP.FtpClient.ConnectAsync(FtpSocketStream stream, CancellationToken token) x 2
   at async Task<List<FtpProfile>> FluentFTP.FtpClient.AutoDetectAsync(bool firstOnly, bool cloneConnection, CancellationToken token) x 2
   at async Task<FtpProfile> FluentFTP.FtpClient.AutoConnectAsync(CancellationToken token)
   at async Task KKManager.Updater.Sources.FtpUpdater.Connect(CancellationToken cancellationToken)+(?) => { }
   at async Task KKManager.Updater.Sources.FtpUpdater.Connect(CancellationToken cancellationToken)
   at async Task<List<UpdateTask>> KKManager.Updater.Sources.FtpUpdater.GetUpdateItems(CancellationToken cancellationToken, bool onlyDiscover, IProgress<float> progressCallback)
   at async Task KKManager.Updater.UpdateSourceManager+<>c__DisplayClass1_1.<GetUpdates>g__DoUpdate|4(?)+DoUpdate(?)
   at async Task<List<UpdateTask>> KKManager.Updater.UpdateSourceManager.GetUpdates(CancellationToken cancellationToken, UpdateSourceBase[] updateSources, string[] filterByGuids, bool onlyDiscover, IProgress<float> progressCallback)
[15:51:48] [Updater] Unexpected crash while updating mods, aborting.
[15:51:48] [Updater] System.IO.InvalidDataException: No valid update sources were found. Either the online update source list could not be accessed, your UpdateSources file is corrupted or in an old format, or KK Manager is outdated. Make sure that you are connected to the internet and not behind a firewall (try using a VPN) and check for KK Manager updates.
  at KKManager.Updater.UpdateSourceManager.GetUpdates (System.Threading.CancellationToken cancellationToken, KKManager.Updater.Sources.UpdateSourceBase[] updateSources, System.String[] filterByGuids, System.Boolean onlyDiscover, System.IProgress`1[T] progressCallback) [0x00301] in <7322c0a9df144253b1b6f1d00a33de60>:0 
  at KKManager.Updater.Windows.ModUpdateProgressDialog.ModUpdateProgress_Shown (System.Object sender, System.EventArgs e) [0x00513] in <7322c0a9df144253b1b6f1d00a33de60>:0 
