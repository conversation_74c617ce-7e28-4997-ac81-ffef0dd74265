■ audio folder
Sound files used in Studio's "External audio" menu.
* Only 8bit and 16bit wav files can be used.
* These files are not saved to the scene files, so avoid using them in scenes that you plan to share.

■ bg folder
This folder contains images for use as Character Maker and Studio backgrounds.
* The images have to be in .png format.
* The images should be in 16:9 aspect ratio.
* The recommended resolution is 1920x1080.

■ cap folder
This folder contains screenshots you took in the game or studio.
You can take screenshots by pressing F9 and F11.

■ cardframe folder
Image files for use when creating a preview image for your character cards.
The back folder contains images placed behind the character, and the front folder contains images placed in front.
* The images have to be in .png format.
* The recommended resolution is 528x720.

■ chara/female and chara/male folders
This is where the characters you create in Character Maker are saved to. Place any downloaded character cards here.
* The character cards are split into two folders based on the gender of the card.
* You have to place cards in the correct folder or they will not appear in the game.

■ coordinate folder
This is where the custom outfits you create in Character Maker are saved to.
All outfits are saved to the same folder.

■ frame folder
Image files for use with your scenes. They are overlayed over the screen.
* The images have to be in .png format.
* The images should be in 16:9 aspect ratio.
* The recommended resolution is 1920x1080.
* Frames don't work properly with high-quality screenshots and are not saved to scene files, therefore it's recommended to avoid using them.

■ pattern folder
Image files that can be used in the "pattern" fields of some items in maker and studio.
* The images should be in .png format.
* The images should be in 1:1 aspect ratio.
* For best results the image should be seamlessly tileable.

■ pattern_thumb folder
Thumbnails for the image files in the pattern folder. They are used in pattern lists to show a preview of the pattern.
* The images should be in .png format.
* The images should be in 1:1 aspect ratio.
* The resolution should be at most 256x256.
* To make your pattern appear in-game you have to add it to both pattern and pattern_thumb folders.
* The thumbnail has to have the same name as the corresponding pattern.

■ Studio\pose folder
This folder contains custom poses you save in studio. Place any downloaded poses here.

■ Studio\scene folder
This folder contains scenes you save in studio. Place any downloaded scenes here.

■ Studio\voicelist folder
This folder contains voice lists you save in studio (in the voice category).

■ config, custom, save and VRSetting folders
This is where your progress and settings are saved. Generally they should not be manually modified. save\game contains story mode saves.
If your game is crashing at startup you can try removing contents of these folders in case some of the settings got corrupted.
