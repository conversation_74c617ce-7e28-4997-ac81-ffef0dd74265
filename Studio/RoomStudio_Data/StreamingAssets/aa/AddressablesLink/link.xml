<linker>
  <assembly fullname="Unity.Addressables, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.AddressableAssets.Addressables" preserve="all" />
  </assembly>
  <assembly fullname="Unity.ResourceManager, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null" preserve="all">
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.LegacyResourcesProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.AssetBundleProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.BundledAssetProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.InstanceProvider" preserve="all" />
    <type fullname="UnityEngine.ResourceManagement.ResourceProviders.SceneProvider" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.GameObject" preserve="all" />
    <type fullname="UnityEngine.Transform" preserve="all" />
    <type fullname="UnityEngine.Camera" preserve="all" />
    <type fullname="UnityEngine.Material" preserve="all" />
    <type fullname="UnityEngine.Texture2D" preserve="all" />
    <type fullname="UnityEngine.Shader" preserve="all" />
    <type fullname="UnityEngine.Object" preserve="all" />
    <type fullname="UnityEngine.Sprite" preserve="all" />
    <type fullname="UnityEngine.RectTransform" preserve="all" />
    <type fullname="UnityEngine.U2D.SpriteAtlas" preserve="all" />
    <type fullname="UnityEngine.Events.ArgumentCache" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentCallGroup" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.PersistentListenerMode" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Events.UnityEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.RectOffset" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.AudioModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.AudioListener" preserve="all" />
    <type fullname="UnityEngine.AudioSource" preserve="all" />
    <type fullname="UnityEngine.AudioReverbFilter" preserve="all" />
    <type fullname="UnityEngine.Audio.AudioMixer" preserve="all" />
    <type fullname="UnityEngine.Audio.AudioMixerGroup" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UI, Version=*******, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.UI.GraphicRaycaster" preserve="all" />
    <type fullname="UnityEngine.UI.Toggle" preserve="all" />
    <type fullname="UnityEngine.UI.Text" preserve="all" />
    <type fullname="UnityEngine.UI.Image" preserve="all" />
    <type fullname="UnityEngine.UI.LayoutElement" preserve="all" />
    <type fullname="UnityEngine.UI.Slider" preserve="all" />
    <type fullname="UnityEngine.UI.CanvasScaler" preserve="all" />
    <type fullname="UnityEngine.UI.Mask" preserve="all" />
    <type fullname="UnityEngine.UI.ToggleGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Button" preserve="all" />
    <type fullname="UnityEngine.UI.Scrollbar" preserve="all" />
    <type fullname="UnityEngine.UI.ContentSizeFitter" preserve="all" />
    <type fullname="UnityEngine.UI.ScrollRect" preserve="all" />
    <type fullname="UnityEngine.UI.RawImage" preserve="all" />
    <type fullname="UnityEngine.UI.InputField" preserve="all" />
    <type fullname="UnityEngine.UI.VerticalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.UI.Shadow" preserve="all" />
    <type fullname="UnityEngine.EventSystems.TouchInputModule" preserve="all" />
    <type fullname="UnityEngine.UI.HorizontalLayoutGroup" preserve="all" />
    <type fullname="UnityEngine.EventSystems.StandaloneInputModule" preserve="all" />
    <type fullname="UnityEngine.EventSystems.EventSystem" preserve="all" />
    <type fullname="UnityEngine.UI.RectMask2D" preserve="all" />
    <type fullname="UnityEngine.UI.AnimationTriggers" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Button/ButtonClickedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ColorBlock" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.FontData" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.InputField/OnChangeEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.InputField/SubmitEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.MaskableGraphic/CullStateChangedEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Navigation" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.ScrollRect/ScrollRectEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Scrollbar/ScrollEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Slider/SliderEvent" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.SpriteState" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.UI.Toggle/ToggleEvent" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.HighDefinition.Runtime, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Rendering.HighDefinition.HDAdditionalCameraData" preserve="all" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FrameSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.FrameSettingsOverrideMask" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.HDPhysicalCamera" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ObsoleteFrameSettings" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.Rendering.HighDefinition.ObsoleteLightLoopSettings" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Assembly-CSharp, Version=*******, Culture=neutral, PublicKeyToken=null">
    <type fullname="RG.Config.TextSetting" preserve="all" />
    <type fullname="RG.Config.VoiceSetting" preserve="all" />
    <type fullname="ILSetUtility.TimeUtility" preserve="all" />
    <type fullname="RG.UI.DialogDataBlock" preserve="all" />
    <type fullname="RG.UI.DialogShopItem" preserve="all" />
    <type fullname="RG.Config.HSetting" preserve="all" />
    <type fullname="Illusion.Component.AutoLayoutCtrl" preserve="all" />
    <type fullname="Manager.HSceneManager" preserve="all" />
    <type fullname="Config.SliderToText" preserve="all" />
    <type fullname="Manager.Game" preserve="all" />
    <type fullname="Manager.Sound" preserve="all" />
    <type fullname="RG.UI.DialogButton" preserve="all" />
    <type fullname="UI_SampleColor" preserve="all" />
    <type fullname="RG.UI.WindowAnimation" preserve="all" />
    <type fullname="UI_RaycastCtrl" preserve="all" />
    <type fullname="RG.UI.DialogText" preserve="all" />
    <type fullname="ExitDialog" preserve="all" />
    <type fullname="RG.Config.ConfigGameSetting" preserve="all" />
    <type fullname="SlotMachineSetting" preserve="all" />
    <type fullname="CircleOutline" preserve="all" />
    <type fullname="RG.UI.AutoRotater" preserve="all" />
    <type fullname="TypefaceAnimatorEx" preserve="all" />
    <type fullname="RG.Config.ConfigVoiceSetData" preserve="all" />
    <type fullname="RG.UI.ColorTint" preserve="all" />
    <type fullname="RG.Config.ConfigToggleElement" preserve="all" />
    <type fullname="Manager.FadePlayer" preserve="all" />
    <type fullname="GraphicDropDown" preserve="all" />
    <type fullname="RG.Config.EnterExitTextComponent" preserve="all" />
    <type fullname="RG.UI.DialogImage" preserve="all" />
    <type fullname="RG.Config.GraphicSetting" preserve="all" />
    <type fullname="SceneAssist.PointerEnterExitAction" preserve="all" />
    <type fullname="RG.Config.ConfigShortcutButton" preserve="all" />
    <type fullname="RG.Scene.Action.Settings.ActionSettings" preserve="all" />
    <type fullname="Manager.Voice" preserve="all" />
    <type fullname="RG.Config.ConfigWindow" preserve="all" />
    <type fullname="RG.Config.CameraSetting" preserve="all" />
    <type fullname="RG.Settings.SceneSettings" preserve="all" />
    <type fullname="Manager.Character" preserve="all" />
    <type fullname="RG.Config.SoundSetting" preserve="all" />
    <type fullname="RG.Config.AppendSetting" preserve="all" />
    <type fullname="CharaCustom.UI_Dropdown/DropdownEvent" preserve="nothing" serialized="true" />
    <type fullname="CharaCustom.UI_Dropdown/OptionData" preserve="nothing" serialized="true" />
    <type fullname="CharaCustom.UI_Dropdown/OptionDataList" preserve="nothing" serialized="true" />
    <type fullname="RG.Config.ConfigWindow/ShortCutGroup" preserve="nothing" serialized="true" />
    <type fullname="RG.Config.SoundSetting/SoundGroup" preserve="nothing" serialized="true" />
    <type fullname="RG.Settings.SceneSettings/SceneDictionary" preserve="nothing" serialized="true" />
    <type fullname="RG.Settings.SceneSettings/SceneParameter" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="IL, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="Illusion.Unity.UI.ColorPicker.Info" preserve="all" />
    <type fullname="Manager.Scene" preserve="all" />
    <type fullname="Illusion.HDRP.PostProcessing.ILPostProcessResources" preserve="all" />
    <type fullname="AssetBundleManager" preserve="all" />
    <type fullname="Illusion.Unity.UI.ColorPicker.PickerRectA" preserve="all" />
    <type fullname="SceneFadeCanvas" preserve="all" />
    <type fullname="Illusion.Unity.UI.ColorPicker.PickerRect/ModeReactiveProperty" preserve="nothing" serialized="true" />
    <type fullname="Illusion.Unity.UI.MouseButtonCheck/Callback" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.TextMeshPro, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="TMPro.TextMeshProUGUI" preserve="all" />
    <type fullname="TMPro.TMP_FontAsset" preserve="all" />
    <type fullname="TMPro.FaceInfo_Legacy" preserve="nothing" serialized="true" />
    <type fullname="TMPro.FontAssetCreationSettings" preserve="nothing" serialized="true" />
    <type fullname="TMPro.KerningTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_Character" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontFeatureTable" preserve="nothing" serialized="true" />
    <type fullname="TMPro.TMP_FontWeightPair" preserve="nothing" serialized="true" />
    <type fullname="TMPro.VertexGradient" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="RaindropFX.HDRP, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="RaindropFX.RDFXResources" preserve="all" />
    <type fullname="RaindropFX.RDFXResources/GenerateShaders" preserve="nothing" serialized="true" />
    <type fullname="RaindropFX.RDFXResources/VolumeShaders" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextRenderingModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.Font" preserve="all" />
  </assembly>
  <assembly fullname="UnityEngine.UIModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEngine.CanvasRenderer" preserve="all" />
    <type fullname="UnityEngine.Canvas" preserve="all" />
    <type fullname="UnityEngine.CanvasGroup" preserve="all" />
  </assembly>
  <assembly fullname="UnityEditor.CoreModule, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null">
    <type fullname="UnityEditor.Audio.AudioMixerSnapshotController" preserve="all" />
  </assembly>
  <assembly fullname="UniRx">
    <type fullname="UniRx.BoolReactiveProperty" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="Unity.RenderPipelines.Core.Runtime">
    <type fullname="UnityEngine.Rendering.BitArray128" preserve="nothing" serialized="true" />
  </assembly>
  <assembly fullname="UnityEngine.TextCoreModule">
    <type fullname="UnityEngine.TextCore.FaceInfo" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.Glyph" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphMetrics" preserve="nothing" serialized="true" />
    <type fullname="UnityEngine.TextCore.GlyphRect" preserve="nothing" serialized="true" />
  </assembly>
</linker>