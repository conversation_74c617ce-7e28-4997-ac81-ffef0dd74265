{"root": [{"assemblyName": "Assembly-CSharp-firstpass", "nameSpace": "EPOOutline", "className": "OutlineEffect", "methodName": "InitMaterials", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "", "className": "StudioScene", "methodName": "Exit", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "RG", "className": "Setup", "methodName": "Initialize", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "Manager", "className": "Config", "methodName": "InitializeBeforeSceneLoad", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Assembly-CSharp", "nameSpace": "Manager", "className": "GameSystem", "methodName": "InitializeBeforeSceneLoad", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineStoryboard", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineCore", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "UpdateTracker", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine", "className": "CinemachineImpulseManager", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Cinemachine", "nameSpace": "Cinemachine.PostFX", "className": "CinemachineVolumeSettings", "methodName": "InitializeModule", "loadTypes": 0, "isUnityClass": false}, {"assemblyName": "Unity.Collections", "nameSpace": "Unity.Collections", "className": "xxHash3", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.Collections", "nameSpace": "", "className": "$BurstDirectCallInitializer", "methodName": "Initialize", "loadTypes": 2, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "UnityEngine.Rendering.HighDefinition", "className": "HDRuntimeReflectionSystem", "methodName": "Initialize", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.HighDefinition.Runtime", "nameSpace": "UnityEngine.Rendering.HighDefinition", "className": "XRSystem", "methodName": "XRSystemInit", "loadTypes": 3, "isUnityClass": true}, {"assemblyName": "Unity.RenderPipelines.Core.Runtime", "nameSpace": "UnityEngine.Rendering", "className": "DebugUpdater", "methodName": "RuntimeInit", "loadTypes": 0, "isUnityClass": true}, {"assemblyName": "Coffee.SoftMaskForUGUI", "nameSpace": "Coffee.UISoftMask", "className": "GraphicConnector", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "UIEffect", "nameSpace": "Coffee.UIEffects", "className": "GraphicConnector", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "UniTask", "nameSpace": "Cysharp.Threading.Tasks", "className": "Player<PERSON>oop<PERSON>el<PERSON>", "methodName": "Init", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Utilities.Editor", "nameSpace": "Sirenix.Utilities", "className": "AssemblyUtilities", "methodName": "DoNothing", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Utilities", "nameSpace": "Sirenix.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Utilities", "nameSpace": "Sirenix.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization", "className": "UnitySerializationInitializer", "methodName": "InitializeRuntime", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization", "className": "UnitySerializationInitializer", "methodName": "InitializeRuntime", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Utilities", "nameSpace": "Sirenix.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization", "className": "UnitySerializationInitializer", "methodName": "InitializeRuntime", "loadTypes": 1, "isUnityClass": false}, {"assemblyName": "Sirenix.Serialization", "nameSpace": "Sirenix.Serialization.Utilities", "className": "UnityVersion", "methodName": "EnsureLoaded", "loadTypes": 1, "isUnityClass": false}]}